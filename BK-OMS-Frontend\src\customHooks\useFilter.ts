import { useState, useEffect, useMemo, useCallback } from "react";
import { useLocation, useNavigate } from "react-router-dom";

export const useTableFilters = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const params = new URLSearchParams(location.search);
  const initialPage = parseInt(params.get("page") || "1", 10);
  const initialPageSize = parseInt(params.get("page_size") || "10", 10);

  const [urlParams, setUrlParams] = useState<string>("");

  const [currentPage, setCurrentPage] = useState<number>(initialPage);
  const [pageSize, setPageSize] = useState<number>(initialPageSize);
  const [filters, setFilters] = useState<Record<string, string | undefined>>(
    () => {
      const filterObj: Record<string, string | undefined> = {};
      params.forEach((value, key) => {
        if (!["page", "page_size"].includes(key)) {
          filterObj[key] = value;
        }
      });
      return filterObj;
    }
  );

  const appliedFilters = useMemo(
    () =>
      Object.keys(filters).filter(
        (key) => key !== "page" && key !== "page_size"
      ),
    [filters]
  );

  const showClearButtons = appliedFilters.length > 0;

  const updateURLParams = (
    updatedFilters: Record<string, string | undefined>
  ) => {
    const params = new URLSearchParams();
    Object.entries(updatedFilters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      }
    });
    setUrlParams(
      params.toString() ? `?${params.toString()}` : location.pathname
    );
  };

  useEffect(() => {
    if (urlParams) {
      navigate(urlParams);
    }
  }, [urlParams, navigate]);

  const handlePageChange = useCallback(
    (page: number, size?: number) => {
      setCurrentPage(page);
      if (size) setPageSize(size);
      const updatedFilters = {
        ...filters,
        page: page.toString(),
        page_size: (size ? size : pageSize).toString(),
      };
      setFilters(updatedFilters);
      updateURLParams(updatedFilters);
    },
    [filters, pageSize, updateURLParams, setFilters]
  );

  const handleFilterChange = useCallback(
    (key: string, value: string) => {
      setFilters((prev) => {
        const newFilters = { ...prev };
        if (value === "All") {
          delete newFilters[key];
        } else {
          newFilters[key] = value;
        }

        newFilters["page"] = "1";
        setCurrentPage(1);
        updateURLParams(newFilters);
        return newFilters;
      });
    },
    [updateURLParams]
  );

  const clearFilter = useCallback(
    (key: string) => {
      setCurrentPage(1);
      setFilters((prev) => {
        const newFilters = { ...prev };
        delete newFilters[key];

        if (newFilters["page"] === "1") {
          delete newFilters["page"];
        }
        updateURLParams(newFilters);
        return newFilters;
      });
    },
    [updateURLParams]
  );

  const clearAllFilters = useCallback(() => {
    setFilters({});
    setCurrentPage(1);

    updateURLParams({});
    navigate(location.pathname);
  }, [location.pathname, navigate]);

  const handlePopState = useCallback(() => {
    const updatedParams = new URLSearchParams(window.location.search);
    const newFilters: Record<string, string | undefined> = {};
    updatedParams.forEach((value, key) => {
      if (!["page", "page_size"].includes(key)) {
        newFilters[key] = value;
      }
    });
    const newPage = parseInt(updatedParams.get("page") || "1", 10) || 1;
    const newPageSize =
      parseInt(updatedParams.get("page_size") || "10", 10) || 10;
    setFilters(newFilters);
    setCurrentPage(newPage);
    setPageSize(newPageSize);
  }, [location.pathname]);

  useEffect(() => {
    window.addEventListener("popstate", handlePopState);
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [handlePopState]);

  return {
    currentPage,
    pageSize,
    filters,
    setFilters,
    appliedFilters,
    showClearButtons,
    setPageSize, 
    updateURLParams,
    handlePageChange,
    handleFilterChange,
    clearFilter,
    clearAllFilters,
  };
};
