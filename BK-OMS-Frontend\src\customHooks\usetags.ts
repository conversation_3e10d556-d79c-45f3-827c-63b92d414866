import { useState, useEffect } from "react";
import axios, { AxiosError } from "axios";
import { axiosInstance } from "../apiCalls";

export interface Tag {
  id: number;
  name: string;
}

const useTags = () => {
  const [tags, setTags] = useState<Tag[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    const controller = new AbortController();

    const fetchTags = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await axiosInstance.get<{ objects: Tag[] }>(
          "/api/menu/tags/",
          { signal: controller.signal }
        );
        if (response.status === 200) {
          setTags(response.data.objects);
        }
      } catch (err: unknown) {
        // console.log("error", err);
        // console.error(err);
        if (axios.isCancel(err)) {
          console.log("Request cancelled");
        } else if (axios.isAxiosError(err)) {
          const axiosErr = err as AxiosError<any>;
          if (axiosErr.response) {
            setError(
              axiosErr.response.data?.message ||
                axiosErr.response.statusText ||
                `Error ${axiosErr.response.status}`
            );
          } else if (axiosErr.request) {
            setError("No response received from server");
          } else {
            setError(axiosErr.message);
          }
        } else {
          // non-Axios error
          setError((err as Error).message);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchTags();
    return () => {
      controller.abort();
    };
  }, []);

  return { tags, error, isLoading };
};

export default useTags;
