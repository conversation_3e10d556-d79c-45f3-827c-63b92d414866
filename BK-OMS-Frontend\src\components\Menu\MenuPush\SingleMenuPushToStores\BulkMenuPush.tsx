import React, {
  useEffect,
  useMemo,
  useState,
  useCallback,
  useTransition,
} from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Checkbox,
  Form,
  message,
  notification,
  Select,
  Tag,
} from "antd";
import { CloseOutlined, LoadingOutlined } from "@ant-design/icons";
import { axiosInstance } from "../../../../apiCalls";
import { useTableFilters } from "../../../../customHooks/useFilter";
import { StoreDataProps } from "../../../../types";
import { Stories } from "../../../Stores/StoreList";
import { WARNING_MESSAGE } from "../Text/Contants";
import FilterButtons from "../../../UI/FilterButton";
import axios from "axios";

const SingleMenuPushToStores: React.FC = () => {
  const [form] = Form.useForm();
  const {
    filters,
    handleFilterChange,
    clearFilter,
    appliedFilters,
    showClearButtons,
    pageSize,
  } = useTableFilters();

  const [storeOptions, setStoreOptions] = useState<StoreDataProps[]>([]);
  const [selectedStores, setSelectedStores] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isPending, startTransition] = useTransition();

  const memoizedFilters = useMemo(() => filters, [filters]);

  const fetchStores = useCallback(
    async (page = 1) => {
      setLoading(true);
      try {
        const response = await axiosInstance.get<Stories>("api/stores/", {
          params: {
            page,
            page_size: pageSize,
            ...memoizedFilters,
          },
        });

        const stores = response.data.objects.map((store) => ({
          id: store.id,
          name: store.name,
          code: store.code,
        }));

        setStoreOptions((prev) =>
          page === 1
            ? stores
            : [
                ...prev,
                ...stores.filter((s) => !prev.find((p) => p.id === s.id)),
              ]
        );
        setTotalCount(response.data.total_count);
        const totalPages = Math.ceil(response.data.total_count / pageSize);
        setHasMore(page < totalPages);
        setCurrentPage(page);
      } catch (error) {
        console.error("Error fetching stores:", error);
        notification.error({
          message: "Error",
          description: "Failed to load stores.",
        });
      } finally {
        setLoading(false);
      }
    },
    [memoizedFilters, pageSize]
  );

  useEffect(() => {
    setCurrentPage(1);
    setHasMore(true);
    fetchStores(1);
  }, [fetchStores]);

  const isAllSelected = useMemo(
    () => selectedStores.size === totalCount && totalCount > 0,
    [selectedStores, totalCount]
  );

  const handleStoreMapping = async () => {
    if (selectedStores.size === 0) {
      message.error("Please select at least one store.");
      return;
    }

    try {
      setIsSaving(true);
      const payload = { stores: Array.from(selectedStores) };
      const response = await axiosInstance.post(
        "api/menu/push-menu-to-multiple-stores/",
        payload
      );
      if ([200, 201].includes(response.status)) {
        message.success(response.data.message);
        setSelectedStores(new Set());
      }
    } catch (error) {
      console.error("Error mapping stores:", error);
      const errorMessage = axios.isAxiosError(error)
        ? error.response?.data?.message ||
          error.response?.data?.error ||
          JSON.stringify(error.response?.data)
        : "Failed to update stores.";
      message.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSelectAllChange = async (e: any) => {
    const checked = e.target.checked;
    if (checked) {
      setLoading(true);
      try {
        const response = await axiosInstance.get<Stories>("api/stores/", {
          params: { page_size: totalCount || 500, ...memoizedFilters },
        });
        const stores = response.data.objects.map((store) => ({
          id: store.id,
          name: store.name,
          code: store.code,
        }));
        const allIds = response.data.objects.map((store) => store.id);
        startTransition(() => {
          setSelectedStores(new Set(allIds));
          setStoreOptions(stores); // update dropdown with full list
        });
      } catch (error) {
        console.error("Error selecting all:", error);
        message.error("Failed to select all stores.");
      } finally {
        setLoading(false);
      }
    } else {
      setSelectedStores(new Set());
    }
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const threshold = 50;

    if (
      target.scrollTop + target.offsetHeight >=
      target.scrollHeight - threshold
    ) {
      if (!loading && hasMore) {
        fetchStores(currentPage + 1);
      }
    }
  };

  const handleStoreSearch = (value: string) => {
    clearTimeout((handleStoreSearch as any).debounce);
    (handleStoreSearch as any).debounce = setTimeout(() => {
      handleFilterChange("search", value);
      setCurrentPage(1);
      setHasMore(true);
      fetchStores(1);
    }, 500);
  };

  const clearFilterHandler = (key: string) => clearFilter(key);

  const formatPaymentMethod = (text: string) =>
    text
      ?.replace(/[^a-zA-Z0-9/\- ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ") || "-";

  const handleRemoveStore = (id: number) => {
    setSelectedStores((prev) => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  };

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div className="header products-headers">
          <div className="title">Bulk Menu Push to Stores</div>
        </div>
      </div>

      <div className="d-flex flex-wrap justify-content-between align-items-center mb-3 mt-4">
        <div className="d-flex flex-wrap align-items-center">
          <FilterButtons
            showClearButtons={showClearButtons}
            appliedFilters={appliedFilters}
            clearFilter={clearFilterHandler}
            formatFilterValue={formatPaymentMethod}
            filters={filters}
          />
          {selectedStores.size > 0 && (
            <div className="ml-3 text-primary">
              <strong>{`${selectedStores.size} store(s) selected`}</strong>
            </div>
          )}
        </div>

        <div className="ml-3">
          <Button
            type="primary"
            className="btn-save"
            onClick={handleStoreMapping}
            disabled={isSaving}
          >
            {isSaving ? "Saving..." : "Generate Menu & Push to Stores"}
          </Button>
        </div>
      </div>

      <Alert
        className="alert alert-warning"
        message={WARNING_MESSAGE}
        type="warning"
      />

      <Card className="mt-3">
        <Card className="mb-4 selected-store-card">
          <div className="d-flex flex-wrap gap-2">
            {Array.from(selectedStores).map((storeId) => {
              const store = storeOptions.find((s) => s.id === storeId);
              return (
                <Tag
                  className="family-font-Poppins fs-6 cursor-pointer"
                  key={storeId}
                  closable
                  onClick={() => handleRemoveStore(storeId)}
                  closeIcon={<CloseOutlined />}
                >
                  {`${store?.name} - (${store?.code})` || ` Store ${storeId}`}
                </Tag>
              );
            })}
          </div>
        </Card>
        <Form
          form={form}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 14 }}
          style={{ maxWidth: 600 }}
        >
          <Form.Item label="Select Stores">
            <Checkbox
              checked={isAllSelected}
              onChange={handleSelectAllChange}
              style={{ marginBottom: 8 }}
            >
              Select All Stores
            </Checkbox>

            <Select
              className="scrollable-select"
              mode="multiple"
              allowClear
              style={{ width: "100%" }}
              placeholder="Search and select stores"
              value={Array.from(selectedStores)}
              onChange={(value: number[]) => setSelectedStores(new Set(value))}
              onSearch={handleStoreSearch}
              onPopupScroll={handleScroll}
              showSearch
              filterOption={false}
              loading={loading || isPending}
            >
              <>
                {storeOptions.map((store) => (
                  <Select.Option
                    className="family-font-Poppins fs-6"
                    key={store.id}
                    value={store.id}
                  >
                    {store.name || `Store ${store.id}`}
                  </Select.Option>
                ))}
                {!loading && hasMore && (
                  <Select.Option disabled key="loading" value="loading">
                    <div className="d-flex justify-content-center align-items-center p-2">
                      <LoadingOutlined spin className="font-size-16" />
                    </div>
                  </Select.Option>
                )}
              </>
            </Select>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default SingleMenuPushToStores;
