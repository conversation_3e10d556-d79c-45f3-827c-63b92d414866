import React, { useState } from "react";
import { Form, Input, Select, Button, message, InputNumber } from "antd";
import { useNavigate } from "react-router-dom";
import "../Stores/addStore/AddStore.css"; // Applying same CSS
import { axiosInstance } from "../../apiCalls";

interface AddModifierGroupProps {
  code: string;
  name: string;
  display_name: string;
  description: string;
  section: string;
  category: string;
  display_type: string;
  channels: number[];
  is_required: boolean;
  max_selectable:number;
}

const AddModifierGroup: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [modifierGroupData, setModifierGroupData] =
    useState<AddModifierGroupProps>({
      code: "",
      name: "",
      display_name: "",
      description: "",
      section: "customize",
      category: "make_into_meal",
      display_type: "image",
      channels: [1],
      is_required: false,
      max_selectable:1,
    });

  const onValuesChange = (
    _: Partial<AddModifierGroupProps>,
    allValues: AddModifierGroupProps
  ) => {
    setModifierGroupData(allValues);
  };

  const onFinish = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.post(
        `api/menu/modifier-groups/`,
        modifierGroupData
      );
      if (response.status === 201) {
        message.success("Modifier Group Created Successfully!");
        navigate("/modifiers-groups");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error response from API:", error.response.data);
        message.error(
          `Failed to create modifier group: ${
            error.response.data.message || "Unknown error"
          }`
        );
      } else {
        console.error("Error creating modifier group:", error);
        message.error("Failed to create modifier group.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h3>Add Modifier Group</h3>
      <Form
        form={form}
        name="add_modifier_group"
        layout="vertical"
        onFinish={onFinish}
        onValuesChange={onValuesChange}
        initialValues={modifierGroupData}
        className="add-store-form"
      >
        <Form.Item
          name="code"
          label="Code"
          rules={[{ required: true, message: "Please enter code" }]}
          className="form-item"
        >
          <Input className="input-field" />
        </Form.Item>

        <Form.Item
          name="name"
          label="Name"
          rules={[{ required: true, message: "Please enter name" }]}
          className="form-item"
        >
          <Input className="input-field" />
        </Form.Item>

        <Form.Item
          name="display_name"
          label="Display Name"
          className="form-item"
        >
          <Input className="input-field" />
        </Form.Item>

        <Form.Item name="description" label="Description" className="form-item">
          <Input.TextArea rows={3} className="input-field" />
        </Form.Item>
        <Form.Item name="section" label="Sections" className="form-item">
          <Select className="input-field">
            <Select.Option value="customize">Customize</Select.Option>
            <Select.Option value="choose_side">Choose Side</Select.Option>
            <Select.Option value="choose_drink">Choose Drink</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item name="category" label="Category" className="form-item">
          <Select className="input-field">
            <Select.Option value="addons">Addons</Select.Option>
            <Select.Option value="drinks">Drinks</Select.Option>
            <Select.Option value="make_into_meal">Make Into Meal</Select.Option>
            <Select.Option value="upsize">Upsize</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="display_type"
          label="Display Type"
          className="form-item"
        >
          <Select className="input-field">
            <Select.Option value="image">Image</Select.Option>
            <Select.Option value="name">Name</Select.Option>
            <Select.Option value="image_and_name">Image and Name</Select.Option>
            <Select.Option value="compact_image_and_name">
              Compact Image and Name
            </Select.Option>
          </Select>
        </Form.Item>
        <Form.Item
          name="max_selectable"
          label="Max Selectable"
          rules={[{ required: true, message: "Please enter Max Selectable" }]}
          className="form-item"
        >
          <InputNumber min={1} max={100} className="input-field" />
        </Form.Item>

        <Form.Item name="channels" label="Channels" className="form-item">
          <Select mode="multiple" className="input-field">
            <Select.Option value={1}>Bk dine In</Select.Option>
            <Select.Option value={2}>BK take away</Select.Option>
            <Select.Option value={3}>Channel 3</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item
          name="position"
          label="Position"
          className="form-item"
        >
          <InputNumber className="input-field" />
        </Form.Item>

        <Form.Item name="is_required" label="Is Required" className="form-item">
          <Select className="input-field">
            <Select.Option value={true}>Yes</Select.Option>
            <Select.Option value={false}>No</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item className="form-item">
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            className="submit-button"
          >
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddModifierGroup;
