import axios from "axios";
import Cookies from "js-cookie";

const baseURL = import.meta.env.VITE_API_URL;
const axiosInstance = axios.create({
  baseURL: baseURL, // Your API base URL
  headers: {
    "Content-Type": "application/json",
  },
});
axiosInstance.interceptors.request.use(
  (config) => {
    const token = Cookies.get("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// axiosInstance.interceptors.response.use(
//   (response) => response,
//   (error) => {
//     if (axios.isCancel(error) || error.code === "ERR_CANCELED") {
//       console.warn("Request canceled", error.message);
//       return Promise.reject(error);
//     }

//     if (error.response) {
//       const { status, data } = error.response;
//       switch (status) {
//         case 401:
//           notification.error({
//             message: "Unauthorized",
//             description: "Session expired. Please log in again.",
//           });
//           window.location.href = "/error401";
//           break;
//         case 403:
//           notification.error({
//             message: "Forbidden",
//             description: "You don't have access to this resource.",
//           });
//           break;
//         case 500:
//           notification.error({
//             message: "Server Error",
//             description: "Something went wrong on the server.",
//           });
//           break;
//         default:
//           notification.error({
//             message: `Error ${status}`,
//             description: data.message || "An error occurred.",
//           });
//       }
//     } else {
//       console.error("Unhandled Error", error);
//     }

//     return Promise.reject(error);
//   }
// );

export { axiosInstance };