import React, { useState } from "react";
import {
  Form,
  Input,
  InputNumber,
  Button,
  message,
  Select,
  Upload,
  notification,
  Spin,
  Alert,
} from "antd";
import { LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import { RcFile } from "antd/es/upload";
import { useNavigate } from "react-router-dom";
import "../Stores/addStore/AddStore.css";
import { axiosInstance } from "../../apiCalls";
import axios from "axios";
import useTags, { Tag } from "../../customHooks/usetags";

interface AddProductProps {
  code: string;
  sku: string;
  name: string;
  display_name: string;
  description: string;
  markdown_description: string;
  short_description: string;
  position: number;
  product_type: string;
  image_large_url: string | null;
  image_thumbnail_url: string | null;
}

const AddProduct: React.FC = () => {
  const { tags, error, isLoading } = useTags();
  // const [dynamicAttributes, setDynamicAttributes] = useState<any>([]);
  const [form] = Form.useForm();
  const navigate = useNavigate();
  // const [loading, setLoading] = useState(false);
  const [imagePreview, setImagePreview] = useState<{
    image_large_url?: string;
    image_thumbnail_url?: string;
  }>({});
  const [uploading, setUploading] = useState(false);

  const [productData, setProductData] = useState<AddProductProps>({
    code: "",
    sku: "",
    name: "",
    display_name: "",
    description: "",
    markdown_description: "",
    short_description: "",
    position: 0,
    product_type: "",
    image_large_url: null,
    image_thumbnail_url: null,
  });

  // useEffect(() => {
  //   if (tags) {
  //     setDynamicAttributes(tags);
  //   }
  // }, [tags]);

  const onValuesChange = (
    _: Partial<AddProductProps>,
    allValues: AddProductProps
  ) => {
    setProductData(allValues);
  };

  const onFinish = async () => {
    // setLoading(true);
    try {
      const response = await axiosInstance.post(
        `api/menu/products/`,
        productData
      );
      if (response.status === 201) {
        message.success("Product Created Successfully!");
        navigate("/products");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error response from API:", error.response.data);
        message.error(
          `Failed to create product: ${
            error.response.data.message || "Unknown error"
          }`
        );
      } else {
        console.error("Error creating product:", error);
        message.error("Failed to create product.");
      }
    }
  };

  const getUploadProps = (
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => ({
    beforeUpload: (file: RcFile) => {
      handleUpload(file, fieldType);
      return false; // Prevent default upload behavior
    },
    showUploadList: false, // Hide default file list UI
  });
  const uploadToS3 = async (
    uploadData: any,
    file: RcFile,
    _fieldType: "image_large_url" | "image_thumbnail_url"
  ) => {
    setUploading(true);
    try {
      if (!uploadData?.url || !uploadData?.fields) {
        // console.error("Invalid upload data:", uploadData);
        return;
      }

      const formData = new FormData();
      Object.entries(uploadData.fields).forEach(([key, value]) => {
        formData.append(key, value as string);
      });
      formData.append("file", file);

      const response = await axios.post(uploadData.url, formData, {
        headers: { "Content-Type": "multipart/form-data" },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.loaded && progressEvent.total) {
            // const percent = Math.round(
            //   (progressEvent.loaded / progressEvent.total) * 100
            // );
            // setUploadProgress((prev) => ({ ...prev, [file.name]: percent }));
          }
        },
      });

      // setUploadSuccess((prev) => ({ ...prev, [file.name]: true }));
      // console.log("key:", key);
      notification.success({
        message: "Upload Successful",
        description: `${file.name} uploaded successfully.`,
      });
      console.log("uploads3:", response);

      // await updateProductImage(uploadData.key, fieldType);
    } catch (error) {
      // setUploadSuccess((prev) => ({ ...prev, [file.name]: false }));

      notification.error({
        message: "Upload Failed",
        description: `Failed to upload ${file.name}.`,
      });
    } finally {
      setUploading(false);
    }
  };
  const getPresignedUrl = async (file: RcFile) => {
    try {
      // console.log("Fetching presigned URL for:", file.name);
      const response = await axiosInstance.post(
        "/api/utilities/get-file-upload-url/",
        {
          file_name: file.name,
          file_type: "thumbnail",
        }
      );

      if (
        !response.data.url ||
        !response.data.url.url ||
        !response.data.url.fields
      ) {
        return null;
      }

      const fileKey = response.data?.url?.fields?.key;
      // console.log("fileKey:",fileKey);
      // console.log("typeOf",typeof(fileKey));

      if (!fileKey) {
        return null;
      }

      return {
        url: response.data.url.url, // Extracting correct URL
        fields: response.data.url.fields, // Extracting correct fields
        key: response.data?.url?.fields?.key,
      };
    } catch (error) {
      notification.error({
        message: "Error",
        description: `Failed to get presigned URL for ${file.name}.`,
      });
      return null;
    }
  };

  const handleUpload = async (
    file: RcFile,
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => {
    const uploadData = await getPresignedUrl(file);
    if (!uploadData) return;

    await uploadToS3(uploadData, file, fieldType);

    // const s3BaseUrl = uploadData.url;
    const s3Key = uploadData.key;
    const fullUrl = `${s3Key}`;

    // Update form and state
    form.setFieldValue(fieldType, fullUrl);
    setProductData((prev) => ({
      ...prev,
      [fieldType]: fullUrl,
    }));
    setImagePreview((prev) => ({
      ...prev,
      [fieldType]: fullUrl,
    }));
  };

  const uploadButton = (
    <div style={{ border: 0, background: "none", textAlign: "center" }}>
      {uploading ? (
        <LoadingOutlined style={{ fontSize: 24 }} />
      ) : (
        <PlusOutlined style={{ fontSize: 24 }} />
      )}
      <div style={{ marginTop: 8 }}>
        {uploading ? "Uploading..." : "Upload"}
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center">
        <Spin />
      </div>
    );
  }

  if (!isLoading && error) {
    return (
      <div className="d-flex justify-content-center align-items-center">
        <Alert type="error" message={error} />
      </div>
    );
  }

  return (
    <div>
      <h3>Add Product</h3>
      <Form
        form={form}
        name="add_product"
        layout="vertical"
        onFinish={onFinish}
        onValuesChange={onValuesChange}
        initialValues={productData}
        className="add-store-form"
      >
        <Form.Item
          name="code"
          label="Product Code"
          rules={[{ required: true, message: "Please enter product code" }]}
          className="form-item"
        >
          <Input className="input-field" />
        </Form.Item>

        <Form.Item
          name="sku"
          label="SKU"
          rules={[{ required: true, message: "Please enter SKU" }]}
          className="form-item"
        >
          <Input className="input-field" />
        </Form.Item>

        <Form.Item
          name="name"
          label="Product Name"
          rules={[{ required: true, message: "Please enter product name" }]}
          className="form-item"
        >
          <Input className="input-field" />
        </Form.Item>

        <Form.Item
          name="display_name"
          label="Display Name"
          className="form-item"
        >
          <Input className="input-field" />
        </Form.Item>

        <Form.Item name="description" label="Description" className="form-item">
          <Input.TextArea rows={3} className="input-field" />
        </Form.Item>

        <Form.Item
          name="markdown_description"
          label="Markdown Description"
          className="form-item"
        >
          <Input.TextArea rows={3} className="input-field" />
        </Form.Item>

        <Form.Item
          name="short_description"
          label="Short Description"
          className="form-item"
        >
          <Input.TextArea rows={2} className="input-field" />
        </Form.Item>

        <Form.Item
          name="position"
          label="Position"
          rules={[{ required: true, message: "Please enter position" }]}
          className="form-item"
        >
          <InputNumber min={1} className="input-field" />
        </Form.Item>

        <Form.Item
          name="product_type"
          label="Product Variant Type"
          rules={[{ required: true, message: "Please select a product type" }]}
        >
          <Select placeholder="Select a product type">
            <Select.Option value="ala_carte">Ala Carte</Select.Option>
            <Select.Option value="meal">Meal</Select.Option>
            <Select.Option value="combo">Combo</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item name="tags" label="Tags">
          <Select mode="multiple" placeholder="Select product types" allowClear>
            {tags.map((type: Tag) => (
              <Select.Option key={type.id} value={type.id}>
                {type.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item label="Upload Large Image">
          <Upload
            listType="picture-card"
            {...getUploadProps("image_large_url")}
          >
            {uploadButton}
          </Upload>
          {imagePreview.image_large_url && (
            <img
              src={`${import.meta.env.VITE_ASSET_URL}/${
                imagePreview.image_large_url
              }`}
              alt="Large Preview"
              style={{ marginTop: 10, maxWidth: "200px", borderRadius: "8px" }}
            />
          )}
        </Form.Item>

        <Form.Item label="Upload Thumbnail">
          <Upload
            listType="picture-card"
            {...getUploadProps("image_thumbnail_url")}
          >
            {uploadButton}
          </Upload>
          {imagePreview.image_thumbnail_url && (
            <img
              src={`${import.meta.env.VITE_ASSET_URL}/${
                imagePreview.image_thumbnail_url
              }`}
              alt="Thumbnail Preview"
              style={{ marginTop: 10, maxWidth: "150px", borderRadius: "8px" }}
            />
          )}
        </Form.Item>

        <Form.Item className="form-item">
          <Button type="primary" htmlType="submit" className="submit-button">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddProduct;
