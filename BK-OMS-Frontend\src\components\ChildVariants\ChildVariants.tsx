import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { ChildVariantProps, StoreMenuProps } from "../../types";
import {
  Table,
  Form,
  Popover,
  Button,
  Input,
  message,
  InputNumber,
  Select,
} from "antd";
import { useNavigate } from "react-router-dom";
import "../Products/Products.css";
import { axiosInstance } from "../../apiCalls";
export interface ChildVariant {
  objects: ChildVariantProps[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: number | null;
  previous_page: number | null;
  total_count: number;
}

const ChildVariants: React.FC = () => {
  const [addNew, setAddNew] = useState(false);

  const { id } = useParams(); // Access the 'storeid' query parameter
  // Access the 'id' query parameter
  // Access the 'name' query parameter
  const [searchInput, setSearchInput] = useState("");

  const [product, setProduct] = useState(0);
  const [search, setSearch] = useState("");

  const [loading, setLoading] = useState<boolean>(false);
  const [childVariants, setChildVariants] = useState<ChildVariant | null>(null);

  const [searchResults, setSearchResults] = useState<StoreMenuProps[]>([]);
  const [visible, setVisible] = useState(false);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const navigate = useNavigate();
  const [form] = Form.useForm();

  // Function to fetch modifiers
  const getSearchResult = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/store-products/${id}/?search=${searchInput}`
      );
      if (response.status === 200) {
        setSearchResults(response.data.objects); // Set the results in the searchResults state
      }
    } catch (error) {
      console.error("Error fetching modifiers", error);
    } finally {
      setLoading(false);
    }
  };
  const onSubmit = async (values: any) => {
    // Create payload object with product name and position

    try {
      const payload = {
        product: Number(product), // Convert storeProduct to a number
        // quantity: Number(values.quantity), // Convert position to a number
        price: Number(values.price),
        section: values.section,
        // position:values.position
      };

      const response = await axiosInstance.post(
        `api/menu/child-variants/${id}/`,
        payload
      );
      if (response.status === 201) {
        await message.success("Child Variants added Successfully!");
        window.location.reload();
      }
      if (response.status === 400) {
        message.error("failed to Child Variants");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error response from API:", error.response.data);
        message.error(
          `failed to Child Variants: ${
            error.response.data.message || "Unknown error"
          }`
        );
      } else {
        message.error("failed to Child Variants.");
      }
    }
  };
  const popoverContent = (
    <div>
      <ul>
        {searchResults.map((result) => (
          <div
            key={result.id}
            onClick={() => {
              setSearchInput(result.variant_name);
              setProduct(result.id);

              setVisible(false); // Close popover after selection
            }}
            style={{ cursor: "pointer", padding: "5px" }}
          >
            {result.variant_name}
          </div>
        ))}
      </ul>
    </div>
  );
  const getChildVariants = async (page: number, search: string) => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/child-variants/${id}/`,
        {
          params: { page, search },
        }
      );
      if (response.status === 200) {
        setChildVariants(response.data);
        setLoading(false);
      } else {
        console.log("Error fetching menu data", response.status);
        setChildVariants(null);
        setLoading(false);
      }
    } catch (error) {
      console.error("Error fetching menu data", error);
      setLoading(false);
    }
  };
  useEffect(() => {
    if (searchInput.length >= 3) {
      getSearchResult(); // Trigger the API call only after 3 characters
    }
  }, [searchInput]);

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "20%",
      render: (text: string, record: ChildVariantProps) => (
        <span
          onClick={() =>
            navigate(
              `/stores/${id}/child-variant/?store=${id}&name=${record.name} &id=${record.id}`
            )
          }
        >
          {text}
        </span>
      ),
    },

    {
      title: "Section",
      dataIndex: "section",
      key: "section",
      width: "10%",
    },
    {
      title: "Price",
      dataIndex: "price",
      key: "price",
      width: "10%",
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      width: "10%",
      render: (isActive: boolean) => (
        <span>{isActive ? "Active" : "Inactive"}</span>
      ),
    },
  ];
  const data: ChildVariantProps[] =
    childVariants?.objects?.map((ChildVariant) => ({
      key: ChildVariant.id,
      id: ChildVariant.id,
      price: ChildVariant.price,
      name: ChildVariant.name || "",
      is_active: ChildVariant.is_active,
      section: ChildVariant.section,
      product: ChildVariant.product,
    })) || [];
  useEffect(() => {
    if (id) {
      getChildVariants(currentPage, search);
    }
  }, [id, currentPage]);

  return (
    <div>
      <div className="main-dashboard-buttons">
        <button
          className="typography"
          onClick={() => {
            setAddNew(!addNew);
          }}
        >
          {" "}
          + Add New{" "}
        </button>
      </div>
      <div className="container product-card-banner">
        <div className="header products-headers">
          <div className="title">Child Variants</div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            <div className="button-serachs">
              <input
                className="search-text"
                placeholder="Search"
                onChange={(e) => {
                  setSearch(e.target.value);
                }}
              />
              <button
                onClick={() => {
                  getChildVariants(currentPage, search);
                }}
              >
                search
              </button>
            </div>
            {/* Search Input with Popover */}
          </div>
        </div>
        {addNew && (
          <Form
            form={form}
            name="horizontal_login"
            layout="inline"
            onFinish={onSubmit}
          >
            <Form.Item
              name="product"
              rules={[
                {
                  required: true,
                  message: "Please input your modifier varient!",
                },
              ]}
            >
              <Input
                placeholder="Product"
                value={searchInput}
                onChange={(e) => {
                  setSearchInput(e.target.value);
                  setVisible(true); // Show popover when typing
                  form.setFieldsValue({ product: e.target.value });
                  // Update form state
                }}
              />
              <Popover
                content={popoverContent}
                title="Search Results"
                trigger="click"
                placement="bottom"
                open={
                  searchInput.length >= 3 && searchResults.length > 0 && visible
                }
                onOpenChange={(open) => setVisible(open)}
              ></Popover>
            </Form.Item>
            {/* 
            <Form.Item
              name="quantity"
              rules={[
                { required: true, message: "Please input Default Quantity" },
              ]}
            >
              <InputNumber
                min={0}
                max={100}
                className="input-field"
                type="quantity"
                placeholder="Quantity"
              />
            </Form.Item> */}
            {/* <Form.Item
              name="position"
              rules={[
                { required: true, message: "Please input Default Quantity" },
              ]}
            >
              <InputNumber
                min={0}
                max={100}
                className="input-field"
                type="position"
                placeholder="Position"
              />
            </Form.Item>  */}
            <Form.Item
              name="price"
              rules={[{ required: true, message: "Please input Max Quantity" }]}
            >
              <InputNumber
                min={0}
                
                className="input-field"
                type="price"
                placeholder="Price"
              />
            </Form.Item>
            <Form.Item
              name="section"
              rules={[{ required: true, message: "Please enter store code" }]}
            >
              <Select className="input-field" defaultValue="----Section----">
                <Select.Option value="customize" className="input-field">
                  Customize
                </Select.Option>
                <Select.Option value="choose_side" className="input-field">
                  Choose Side
                </Select.Option>
                <Select.Option value="choose_drink" className="input-field">
                  Choose Drink
                </Select.Option>
              </Select>
            </Form.Item>
            <Form.Item shouldUpdate>
              {() => (
                <Button className="typography" htmlType="submit">
                  Submit
                </Button>
              )}
            </Form.Item>
          </Form>
        )}
      </div>

      <div className="pt-4 mt-4">
        <Table<ChildVariantProps>
          columns={columns}
          dataSource={data}
          loading={loading}
          pagination={{
            current: currentPage,
            total: childVariants?.total_count || 0,
            pageSize: 10,
            showSizeChanger: false,
            onChange: (page) => setCurrentPage(page),
            itemRender: (_, type) => {
              if (type === "prev") {
                return <a>Previous</a>;
              }
              if (type === "next") {
                return <a>Next</a>;
              }
              return null;
            },
          }}
          scroll={{ x: 1100, y: 700 }}
        />
        ,
      </div>
    </div>
  );
};

export default ChildVariants;
