import React, { useEffect, useState } from "react";
import {
  Form,
  Input,
  Button,
  message,
  Select,
  Popover,
  InputNumber,
} from "antd";

import { useNavigate, useParams } from "react-router-dom";
import "../../../Stores/addStore/AddStore.css";
import { axiosInstance } from "../../../../apiCalls";
import { ProductVariantProps } from "../../../../types/Products";
interface AddProductVariantChildVariantProps {
  section: string;
  child: number;
  parent: number;
  position: number;
}

const AddProductVariantChildVariant: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  // const { id } = useParams() as { id: string };
  // const { code } = useParams() as { code: string };
  const { variantId } = useParams() as { variantId: string };
  const [searchInput, setSearchInput] = useState("");
  // const [loading, setLoading] = useState<boolean>(false);
  const [parentId, setParentId] = useState(0);
  const [visable, setVisible] = useState(false);
  const [searchResults, setSearchResults] = useState<ProductVariantProps[]>([]);

  const [storeData, setStoreData] =
    useState<AddProductVariantChildVariantProps>({
      section: "",
      child: 0,
      parent: 0,
      position: 1,
    });

  const onValuesChange = (
    _: Partial<AddProductVariantChildVariantProps>,
    allValues: AddProductVariantChildVariantProps
  ) => {
    setStoreData({ ...allValues, parent: Number(variantId), child: parentId });
  };

  const getSearchResult = async () => {
    // setLoading(true);
    try {
      const response = await axiosInstance.get(
        // `api/menu/v2/product-variants/${code}/?search=${searchInput}`
        `api/menu/v2/product-variants-list/?search=${searchInput}`
      );
      if (response.status === 200) {
        setSearchResults(response.data.objects); // Set the results in the searchResults state
      } else {
        console.error("Error fetching modifiers", response.status);
      }
    } catch (error) {
      console.error("Error fetching modifiers", error);
    } finally {
      // setLoading(false);
    }
  };
  useEffect(() => {
    if (searchInput.length >= 3) {
      getSearchResult(); // Trigger the API call only after 3 characters
    }
  }, [searchInput]);

  const onFinish = async () => {
    try {
      const response = await axiosInstance.post(
        `api/menu/v2/child-variants/${variantId}/`,
        storeData
      );
      if (response.status === 201) {
        message.success("Variant Child Created Successfully!");
        navigate(`/variants/${variantId}?tab=Child_Variants`);
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error response from API:", error.response.data);
        message.error(
          `Failed to create store: ${
            error.response.data.message || "Unknown error"
          }`
        );
      } else {
        console.error("Error creating store:", error);
        message.error("Failed to create store.");
      }
    }
  };
  const popoverContent = (
    <div>
      <ul>
        {searchResults.map((result) => (
          <div
            key={result.id}
            onClick={() => {
              setSearchInput(result.name);
              setParentId(result.id);
              setVisible(false); // Close popover after selection
            }}
            style={{ cursor: "pointer", padding: "5px" }}
          >
            {result.name}
          </div>
        ))}
      </ul>
    </div>
  );

  return (
    <div>
      <h3>Add Child Variant to Product Variant</h3>
      <Form
        form={form}
        name="add_store"
        layout="vertical"
        onFinish={onFinish}
        onValuesChange={onValuesChange}
        initialValues={storeData}
        className="add-store-form"
      >
        <Form.Item
          name="child"
          label="Child Variant"
          rules={[{ required: true, message: "Please enter child Variant" }]}
          className="form-item"
        >
          <div>
            <Input
              placeholder="Search with code or name"
              value={searchInput}
              onChange={(e) => {
                setSearchInput(e.target.value);
                setVisible(true); // Show popover when typing
                form.setFieldsValue({ product_variant: e.target.value }); // Update form state
              }}
            />
            <Popover
              content={popoverContent}
              title="Search Results"
              trigger="click"
              placement="bottom"
              open={
                searchInput.length >= 3 && searchResults.length > 0 && visable
              }
              onOpenChange={(open) => setVisible(open)}
            />
          </div>
        </Form.Item>

        <Form.Item
          name="section"
          label="section"
          rules={[{ required: true, message: "Please select a product type" }]}
          className="form-item"
        >
          <Select placeholder="Select section" className="input-field">
            <Select.Option value="customize">Customize</Select.Option>
            <Select.Option value="choose_side">Choose Side</Select.Option>
            <Select.Option value="choose_drink">Choose Drink</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item
          name="position"
          label="Position"
          rules={[{ required: true, message: "Please enter position" }]}
        >
          <InputNumber />
        </Form.Item>

        <Form.Item className="form-item">
          <Button type="primary" htmlType="submit" className="submit-button">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddProductVariantChildVariant;
