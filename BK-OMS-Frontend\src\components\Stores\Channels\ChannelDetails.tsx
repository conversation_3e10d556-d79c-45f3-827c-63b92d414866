import{  useState } from "react";
import { <PERSON>,Spin  } from "antd";
// import { useParams } from "react-router-dom";
// import { useLocation } from "react-router-dom";
// import { axiosInstance } from "../../apiCalls";
// import { StoreDataProps } from "../../types";
// import StoreModiferGroups from "./StroreModiferGroups/StoreModiferGroups";
// import StoreCatagory from "./StoreCatagory";
import "../../Products/Products.css";
// import StoreMenu from "./storeMenu/StoreMenu";
// import { set } from "react-hook-form";
// import StoreCatagory from "../StoreCatagory";
// import StoreModiferGroup from "../../Modifiers/StroreModifierGroup/StoreModiferGroup";
// import StoreModiferGroups from "../StroreModiferGroups/StoreModiferGroups";
// import ListStoreModifiers from "./StoreModifiers/ListStoreModifiers";
// import ChannelsList from "./Channels/ChannelsList";
const tabList = [
    {
      key: "tab1",
      tab: "Categories",
    },
    {
      key: "tab2",
      tab: "Modifier Groups",
    },
    {
      key: "tab3",
      tab: "Child Varinants",
    },

  ];
const ChannelDetails = () => {
      // const location = useLocation();
      // const queryParams = new URLSearchParams(location.search);
    
      // const storeId = queryParams.get("storeId"); // Access the 'storeid' query parameter
      // const channelid = queryParams.get("channel");
      // const [loading, setLoading] = useState(false);
      const [activeTabKey1, setActiveTabKey1] = useState<string>("tab1");
  //     const contentList: Record<string, React.ReactNode> = {
  //       tab1:<StoreCatagory />
  //  ,
        
  //       tab2: <StoreModiferGroups />

  //     };
      const onTab1Change = (key: string) => {
        setActiveTabKey1(key);
      };
      return (
        <>
       
          <div className="loader-container">
            <Spin size="large" />
          </div>

          <Card
            style={{ width: "100%" }}
            title={<h3></h3>}
            tabList={tabList}
            activeTabKey={activeTabKey1}
            onTabChange={onTab1Change}
            extra={
              <div className="main-dashboard-buttons">
                <button className="typography" >
                  {" "}
                  +Sync Products{" "}
                </button>
                <button className="typography" >
                  {" "}
                  + Download Products CSV{" "}
                </button>{" "}
                <button
                  className="typography"

                >
                  {" "}
                  + Bulk Update{" "}
                </button>

              </div>
            }
          >
           
          </Card>
   
    
          <br />
          <br />
    
        </>
      );
}

export default ChannelDetails
