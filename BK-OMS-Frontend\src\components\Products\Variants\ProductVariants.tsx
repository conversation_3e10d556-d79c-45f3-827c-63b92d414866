import { ProductVariantProps } from "../../../types/Products";
import { Typography } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import StoreTabs from "../../../reusableComponents/store/StoreTabs";


const { Link } = Typography;

const ProductVariants = () => {
  const navigate = useNavigate();
  const { code } = useParams() as { code: string };
  // const { id } = useParams() as { id: string };
  

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "code",
      fixed: "left" as "left",
      render: (text: string, record: ProductVariantProps) => (
        <span
          onClick={() =>
            navigate(
              `/variants/${record.id}`
            )
          }
        >
          <Link>{text}</Link>
        </span>
      ),
    },
    {
      title: "display name",
      dataIndex: "display_name",
      key: "display_name",
    },
    {
      title: "Id",
      dataIndex: "id",
      key: "id",
    },
    {
      title: "POS",
      dataIndex: "code",
      key: "code",
    },
    // {
    //   title: "Status",
    //   dataIndex: "is_active",
    //   key: "is_active",
    //   render: (isActive: boolean) => (
    //     <span>{isActive ? "Active" : "Inactive"}</span>
    //   ),
    // },
  ];
  const mapData = (response: any): ProductVariantProps[] =>
    response.objects.map((item: any) => ({
      key: item.id,
      id: item.id,
      name: item.name,
      display_name: item.display_name,
      code: item.code,
      description: item.description,
      product_type: item.product_type,
      base_product: item.base_product,
    }));

  return (

      <StoreTabs
        id={code}
        apiEndpoint="api/menu/v2/product-variants"
        name="Product Variants"
        columns={columns}
        dataMapper={mapData}
        // itemPath={itemPath}
        add={`/products/${code}/variants/add`}
      />

  );
};

export default ProductVariants;
