import React, { useMemo } from "react";
import { Table, TableProps } from "antd";

export interface ReusableTableProps<T> extends TableProps<T> {}

const DataTable = <T extends object>({
  pagination = false,
  scroll = { x: 1000 },
  ...props
}: ReusableTableProps<T>) => {
  const memoizedProps = useMemo(() => {
    return {
      pagination,
      scroll,
      ...props,
    };
  }, [pagination, scroll, props]);

  return <Table<T> {...memoizedProps} className="table-container border-1 border-gray-200 rounded-md rounded-b-none" />;
};

export default React.memo(DataTable) as typeof DataTable;
