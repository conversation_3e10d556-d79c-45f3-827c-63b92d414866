import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { ModifierGroupProps } from "../../../types";
import { CSS } from "@dnd-kit/utilities";
import { message, Modal, Typography } from "antd";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { axiosInstance } from "../../../apiCalls";
import { useTableFilters } from "../../../customHooks/useFilter";
import SearchBox from "../../UI/SearchBox";
import DataTable from "../../UI/DataTable/DataTable";

const { Link } = Typography;

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  "data-row-key": string;
}

const DragRow: React.FC<RowProps & { dragEnabled: boolean }> = ({
  dragEnabled,
  ...props
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props["data-row-key"] });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: dragEnabled ? "move" : "default",
    ...(isDragging ? { position: "relative", zIndex: 9999 } : {}),
  };

  return (
    <tr
      {...props}
      ref={setNodeRef}
      style={style}
      {...(dragEnabled ? { ...attributes, ...listeners } : {})}
    />
  );
};

interface CategoryWithKey extends ModifierGroupProps {
  key: string;
}

const StoreModifierGroups: React.FC<{ storeId: string }> = ({ storeId }) => {
  const navigate = useNavigate();
  const [modifierGroups, setModifierGroups] = useState<CategoryWithKey[]>([]);
  const [loading, setLoading] = useState(false);

  const { currentPage, pageSize, filters, handleFilterChange } =
    useTableFilters();

  const [editingId, _setEditingId] = useState<number | null>(null);
  // const [editingPosition, setEditingPosition] = useState<number | null>(null);
  const [dragEnabled, _setDragEnabled] = useState(false);
  const codeInputRef = useRef<HTMLInputElement>(null);
  const [search, setSearch] = useState("");

  const memoizedFilters = useMemo(() => filters, [filters]);

  const fetchModifierGroups = useCallback(async () => {
    setLoading(true);
    try {
      const { data, status } = await axiosInstance.get(
        `/api/menu/store-modifier-groups/${storeId}/`,
        {
          params: {
            page: currentPage,
            page_size: pageSize,
            ...memoizedFilters,
          },
        }
      );
      if (status === 200) {
        const sorted = data
          .map((item: any) => ({
            ...item,
            key: item.id.toString(),
          }))
          .sort((a: any, b: any) => a.position - b.position);
        setModifierGroups(sorted);
      }
    } catch (err) {
      console.error(err);
      message.error("Failed to load modifier groups.");
    } finally {
      setLoading(false);
    }
  }, [storeId, currentPage, pageSize, memoizedFilters]);

  useEffect(() => {
    fetchModifierGroups();
  }, [fetchModifierGroups]);

  useEffect(() => {
    if (editingId !== null) {
      codeInputRef.current?.focus();
    }
  }, [editingId]);

  const patchModifierGroup = async (
    id: number,
    updates: Partial<{ position: number; is_active: boolean }>
  ) => {
    return axiosInstance.patch(
      `/api/menu/store-modifier-groups-details/${storeId}/${id}/`,
      updates
    );
  };

  const handleStatusChange = (id: number, isActive: boolean) => {
    Modal.confirm({
      title: isActive ? "Activate Modifier Group" : "Deactivate Modifier Group",
      content: `Are you sure you want to ${
        isActive ? "activate" : "deactivate"
      } this modifier group?`,    
      onOk: async () => {
        try {
          const res = await patchModifierGroup(id, { is_active: isActive });
          if (res.status === 200) {
            message.success("Status updated successfully");
            fetchModifierGroups();
          }
        } catch {
          message.error("Failed to update status");
        }
      },
    });
  };

  // const handleSubmitPosition = async (id: number) => {
  //   if (editingPosition !== null) {
  //     try {
  //       const res = await patchModifierGroup(id, { position: editingPosition });
  //       if (res.status === 200) {
  //         setEditingId(null);
  //         setEditingPosition(null);
  //         setDragEnabled(false); // disable drag after save
  //         fetchModifierGroups();
  //       }
  //     } catch {
  //       message.error("Failed to update position");
  //     }
  //   }
  // };

  // const onDragEnd = async ({ active, over }: DragEndEvent) => {
  //   if (!dragEnabled) return;
  //   if (!over) return;

  //   if (active.id !== over.id) {
  //     const oldIndex = modifierGroups.findIndex((c) => c.key === active.id);
  //     const newIndex = modifierGroups.findIndex((c) => c.key === over.id);
  //     const newOrder = arrayMove(modifierGroups, oldIndex, newIndex);
  //     setModifierGroups(newOrder);

  //     try {
  //       const responses = await Promise.all(
  //         newOrder.map((item, idx) =>
  //           patchModifierGroup(item.id, { position: idx + 1 })
  //         )
  //       );
  //       if (responses.every((r) => r.status === 200)) {
  //         message.success("Modifier groups reordered");
  //         fetchModifierGroups();
  //       }
  //     } catch {
  //       message.error("Failed to save new order");
  //     }
  //   }
  // };
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (!dragEnabled || !over || active.id === over.id) return;

    const oldIndex = modifierGroups.findIndex((c) => c.key === active.id);
    const newIndex = modifierGroups.findIndex((c) => c.key === over.id);
    const newOrder = arrayMove(modifierGroups, oldIndex, newIndex);
    setModifierGroups(newOrder);
  };

  const handleClearSearch = () => {
    setSearch("");
    handleFilterChange("search", "");
  };
  // const handleSaveNewOrder = async () => {
  //   try {
  //     const updatedmodifierGroups = modifierGroups.map((item, idx) => ({
  //       ...item,
  //       position: idx + 1,
  //     }));
  //     const response = await axiosInstance.patch(
  //       `/api/menu/store-modifier-variants-position-update/${storeId}/`,
  //       {
  //         list: updatedmodifierGroups,
  //       }
  //     );
  //     if (response.status === 200) {
  //       message.success("Modifiers reordered successfully.");
  //       fetchModifierGroups();
  //     }
  //   } catch {
  //     message.error("Failed to save new order.");
  //   }
  // };

  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 1 } })
  );

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render: (text: string, record: ModifierGroupProps) => (
        <Link
          onClick={() =>
            navigate(
              `/store/${record.store}/modifier-group/${record.id}/?name=${record.name}`
            )
          }
        >
          {text}
        </Link>
      ),
    },
    {
      title: "Section",
      dataIndex: "section",
      key: "section",
    },
    // {
    //   title: "Position",
    //   dataIndex: "position",
    //   key: "position",
    //   render: (_: any, record: ModifierGroupProps) => (
    //     <>
    //       {record.position}                           
    //       <Button
    //         type="link"
    //         onClick={() => {
    //           setDragEnabled(true);
    //           message.info("Drag and drop is now enabled.");
    //         }}
    //       >
    //         <EditOutlined className="btn-edit-pencil" />
    //       </Button>
    //       {dragEnabled && (
    //         <Button
    //           icon={<CheckOutlined />}
    //           onClick={() => {
    //             setDragEnabled(false);
    //             handleSaveNewOrder();
    //           }}
    //         />
    //       )}
    //     </>
    //   ),
    // },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      // render: (isActive: boolean, record: ModifierGroupProps) => (
      //   <Switch
      //     checked={isActive}
      //     onChange={(checked) => handleStatusChange(record.id, checked)}
      //     style={{
      //       backgroundColor: isActive ? "#ff8732" : undefined,
      //     }}
      //   />
      // ),
      render: (isActive: boolean, record: ModifierGroupProps) => (
        <div
          className={`switch-button ${isActive ? "checked" : ""}`}
          onClick={() => handleStatusChange(record.id, !isActive)}
        >
          <span className="switch-label">
            {isActive ? <CheckOutlined /> : <CloseOutlined />}
          </span>
          <div className="switch-handle" />
        </div>
      ),
    },
  ];

  return (
    <>
      <SearchBox
        value={search}
        onChange={(newValue: string) => setSearch(newValue)}
        onSearch={() => handleFilterChange("search", search)}
        onClear={handleClearSearch}
        placeholder="Search modifier groups"
      />

      <DndContext
        sensors={sensors}
        modifiers={[restrictToVerticalAxis]}
        onDragEnd={onDragEnd}
      >
        <SortableContext
          items={modifierGroups.map((item) => item.key)}
          strategy={verticalListSortingStrategy}
        >
          <DataTable
            loading={loading}
            columns={columns}
            dataSource={modifierGroups}  
            components={{
              body: {
                row: (props: RowProps) => (
                  <DragRow {...props} dragEnabled={dragEnabled} />
                ),
              },
            }}
            rowKey="key"
          />
        </SortableContext>
      </DndContext>
    </>
  );
};

export default StoreModifierGroups;
