import React, { useEffect, useMemo, useState } from "react";
import { Button, message, notification } from "antd";
import { Link, useNavigate } from "react-router-dom";
import type { TableRowSelection } from "antd/es/table/interface";
import { axiosInstance } from "../../../../apiCalls";
import axios from "axios";
import { useTableFilters } from "../../../../customHooks/useFilter";
import FilterButtons from "../../../UI/FilterButton";
import DataTable from "../../../UI/DataTable/DataTable";
import CommonPagination from "../../../UI/Pagination/commonPagination";
import SearchBox from "../../../UI/SearchBox";
import { StoreDataProps } from "../../../../types";
import { Stories } from "../../../Stores/StoreList";

const SingleMenuPushToStores: React.FC = () => {
  const {
    currentPage,
    pageSize,
    filters,
    appliedFilters,
    showClearButtons,
    handlePageChange,
    handleFilterChange,
    clearFilter,
  } = useTableFilters();
  const navigate = useNavigate();

  // State variables
  const [storeOptions, setStoreOptions] = useState<StoreDataProps[]>([]);
  const [selectedStores, setSelectedStores] = useState<Set<number>>(new Set());
  const [initialSelectedStores, setInitialSelectedStores] = useState<
    Set<number>
  >(new Set());
  const [newlySelectedStores, setNewlySelectedStores] = useState<Set<number>>(
    new Set()
  );
  const [loading, setLoading] = useState<boolean>(false);
  // const [currentPage, setCurrentPage] = useState<number>(1);
  // const [pageSize, setPageSize] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  // const [searchTerm, setSearchTerm] = useState<string>("");

  const [searchValue, setSearchValue] = useState<string>("");

  const memoizedFilters = useMemo(() => filters, [filters]);

  // Add a new state for tracking if "select all" is active
  const [selectAllStores, setSelectAllStores] = useState<boolean>(false);
  const [allStoreIds, setAllStoreIds] = useState<number[]>([]);

  useEffect(() => {
    fetchStores();
  }, [currentPage, pageSize, memoizedFilters]);

  const fetchStores = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get<Stories>(`api/stores/`, {
        params: {
          page: currentPage,
          page_size: pageSize,
          ...memoizedFilters,
        },
      });

      if (response.data?.objects) {
        const fetchedStores = response.data.objects.map(
          (store: StoreDataProps) => ({
            id: store.id,
            name: store.name,
            code: store.code,
          })
        );

        setStoreOptions(fetchedStores);
        setTotalCount(response.data.total_count);

        // Don't interfere with existing selections when navigating pages
        // Only set initial selections on first load
        if (selectedStores.size === 0 && newlySelectedStores.size === 0) {
          const initiallySelected = new Set(
            fetchedStores
              .filter((store: StoreDataProps) => store.is_active)
              .map((store: any) => store.id)
          );
          setInitialSelectedStores(initiallySelected);
          setSelectedStores(initiallySelected);
        }
      } else {
        setStoreOptions([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching stores:", error);
      notification.error({
        message: "Error",
        description: "Failed to load stores.",
      });
    } finally {
      setLoading(false);
    }
  };

  // Add a function to fetch all store IDs
  const fetchAllStoreIds = async () => {
    try {
      console.log("Fetching all store IDs...");
      const response = await axiosInstance.get<Stories>(`api/stores/`, {
        params: {
          page_size: 9999, // Large number to get all stores
          ...memoizedFilters,
        },
      });

      if (response.data?.objects) {
        const ids = response.data.objects.map((store) => store.id);
        console.log(`Fetched ${ids.length} store IDs:`, ids);
        setAllStoreIds(ids);
        return ids;
      }
      console.log("No stores found in response");
      return [];
    } catch (error) {
      console.error("Error fetching all store IDs:", error);
      return [];
    }
  };

  //  Handle row selection logic
  const rowSelection: TableRowSelection<StoreDataProps> = {
    selectedRowKeys: Array.from(selectedStores),
    onChange: (selectedRowKeys: React.Key[]) => {
      const newSelectedStores = new Set(selectedRowKeys.map(Number));
      setSelectedStores(newSelectedStores);
      setNewlySelectedStores(newSelectedStores);
      // Reset select all state if manual selection changes
      setSelectAllStores(false);
    },
    onSelect: (record: any, selected: boolean) => {
      // If individual selection happens, disable "select all" mode
      if (selectAllStores) {
        setSelectAllStores(false);
      }

      setSelectedStores((prev) => {
        const newSet = new Set(prev);

        if (selected) {
          newSet.add(record.id);
          setNewlySelectedStores((prev) => new Set(prev).add(record.id));
        } else {
          newSet.delete(record.id);
          setNewlySelectedStores((prev) => {
            const updatedNewlySelected = new Set(prev);
            updatedNewlySelected.delete(record.id);
            return updatedNewlySelected;
          });
        }
        return newSet;
      });
    },
    onSelectAll: async (
      selected: boolean,
      _selectedRows: StoreDataProps[],
      _changeRows: StoreDataProps[]
    ) => {
      if (selected) {
        // Directly select all stores across all pages without modal
        setLoading(true);
        try {
          const allIds = await fetchAllStoreIds();
          setSelectAllStores(true);
          setSelectedStores(new Set(allIds));
          setNewlySelectedStores(new Set(allIds));
          message.success(
            `Selected ${allIds.length} stores across all pages`
          );
        } catch (error) {
          message.error("Failed to select all stores");
        } finally {
          setLoading(false);
        }
      } else {
        // If deselecting all, clear the selection
        setSelectedStores(new Set());
        setNewlySelectedStores(new Set());
        setSelectAllStores(false);
      }
    },
    columnWidth: "5%",
  };

  const handleStoreMapping = async () => {
    // If no stores selected, show error
    if (selectedStores.size === 0) {
      message.error("Please select at least one store.");
      return;
    }

    // Use all selected stores (whether from select all or individual selection)
    const finalStoreIds = Array.from(selectedStores);

    const payload = {
      stores: finalStoreIds,
    };

    console.log("Payload being sent:", payload);
    console.log("Selected stores count:", finalStoreIds.length);

    try {
      const response = await axiosInstance.post(
        "api/menu/push-menu-to-multiple-stores/",
        payload
      );
      if (response.status === 201 || response.status === 200) {
        message.success(`${response.data.message}`);
      }
    } catch (error) {
      console.error("Error mapping stores:", error);

      let errorMessage = "Failed to update stores.";

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 400) {
          errorMessage = "Please Select  at least one store.";
        } else {
          errorMessage =
            error.response?.data?.message ||
            error.response?.data?.error ||
            JSON.stringify(error.response?.data) ||
            errorMessage;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      message.error(errorMessage);
    }
  };

  const handleSearchChange = (value: string) => {
    handleFilterChange("search", value);
  };

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search") {
      setSearchValue("");
    }
  };

  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";

    return (
      text
        // .replace(/[^a-zA-Z0-9 ]/g, " ")
        .replace(/[^a-zA-Z0-9\/\- ]/g, " ")
        .trim()
        .split(" ")
        .map(
          (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(" ")
    );
  };

  const columns = [
    {
      title: "Select All",
      dataIndex: "",
      key: "",
      width: "5%",
      fixed: "left" as "left",
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "40%",
      render: (text: string, record: any) =>
        text ? (
          <Link
            className="common-link text-decoration-none"
            to={`/stores/${record.id}`}
          >
            {text}
          </Link>
        ) : (
          "N/A"
        ),
    },
    { title: "Code", dataIndex: "code", key: "code", width: "30%" },
  ];

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div className="header products-headers">
          <div className="title">Single Menu Push to Stores</div>
        </div>
      </div>

      <div className="d-flex flex-wrap justify-content-between align-items-center mb-3 mt-4">
        <div className="d-flex flex-wrap align-items-center">
          <div className="search-btn-driver">
            <SearchBox
              placeholder="Search by code & name"
              value={searchValue}
              onChange={setSearchValue}
              onSearch={() => handleSearchChange(searchValue)}
            />
          </div>
          <div>
            <FilterButtons
              showClearButtons={showClearButtons}
              appliedFilters={appliedFilters}
              clearFilter={clearFilterHandler}
              formatFilterValue={formatPaymentMethod}
              filters={filters}
            />
          </div>
          {/* Show selection status */}
          {selectedStores.size > 0 && (
            <div className="ml-3 text-primary">
              <strong>
                {selectAllStores
                  ? `All ${selectedStores.size} stores selected across all pages`
                  : `${selectedStores.size} store(s) selected`
                }
              </strong>
              {selectAllStores && (
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    setSelectedStores(new Set());
                    setNewlySelectedStores(new Set());
                    setSelectAllStores(false);
                  }}
                >
                  Clear All
                </Button>
              )}
            </div>
          )}
        </div>
        <div className="ml-3">
          <Button
            type="primary"
            className="btn-save"
            onClick={handleStoreMapping}
            disabled={loading}
          >
            {loading ? `Saving...` : `Save Single Menu Push`}
          </Button>
          <Button
            type="default"
            className="btn-cancel"
            onClick={() => navigate(-1)}
          >
            Cancel
          </Button>
        </div>
      </div>

      <>
        <div className="mt-3">
          <DataTable
            rowSelection={rowSelection}
            dataSource={storeOptions}
            columns={columns}
            loading={loading}
            rowKey="id"
            pagination={false}
            scroll={{ x: "max-content" }}
          />
        </div>

        <div className="d-flex justify-content-end mt-3 mb-3">
          <CommonPagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            onChange={handlePageChange}
            showSizeChanger
          />
        </div>
      </>
    </div>
  );
};

export default SingleMenuPushToStores;
