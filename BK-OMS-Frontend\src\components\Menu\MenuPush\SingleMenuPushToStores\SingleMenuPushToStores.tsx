import React, { useEffect, useMemo, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, message, notification, Tag } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { Link } from "react-router-dom";
import type { TableRowSelection } from "antd/es/table/interface";
import { axiosInstance } from "../../../../apiCalls";
import axios from "axios";
import { useTableFilters } from "../../../../customHooks/useFilter";
import FilterButtons from "../../../UI/FilterButton";
import DataTable from "../../../UI/DataTable/DataTable";
import CommonPagination from "../../../UI/Pagination/commonPagination";
import SearchBox from "../../../UI/SearchBox";
import { StoreDataProps } from "../../../../types";
import { Stories } from "../../../Stores/StoreList";
import { WARNING_MESSAGE } from "../Text/Contants";

const SingleMenuPushToStores: React.FC = () => {
  const {
    filters,
    appliedFilters,
    showClearButtons,
    handleFilterChange,
    clearFilter,
  } = useTableFilters();

  const [storeOptions, setStoreOptions] = useState<StoreDataProps[]>([]);
  const [selectedStores, setSelectedStores] = useState<Set<number>>(new Set());
  const [initialSelectedStores, setInitialSelectedStores] = useState<
    Set<number>
  >(new Set());
  const [newlySelectedStores, setNewlySelectedStores] = useState<Set<number>>(
    new Set()
  );
  const [loading, setLoading] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);

  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);

  const [searchValue, setSearchValue] = useState<string>("");
  const [isAllSelected, setIsAllSelected] = useState<boolean>(false);
  const [isDeselectingAll, setIsDeselectingAll] = useState<boolean>(false);

  const memoizedFilters = useMemo(() => filters, [filters]);

  const [selectedStoreMap, setSelectedStoreMap] = useState<
    Map<number, StoreDataProps>
  >(new Map());

  // Track if global Select All was recently used
  const [selectAllActive, setSelectAllActive] = useState<boolean>(false);

  // Fetch all store IDs for Select All
  // const fetchAllStoreIds = async () => {
  //   try {
  //     const response = await axiosInstance.get<Stories>(`api/stores/`, {
  //       params: {
  //         page_size: 9999,
  //         ...memoizedFilters,
  //       },
  //     });

  //     if (response.data?.objects) {
  //       const ids = response.data.objects.map((store) => store.id);
  //       const map = new Map<number, StoreDataProps>();
  //       response.data.objects.forEach((store) => {
  //         map.set(store.id, {
  //           id: store.id,
  //           name: store.name,
  //           code: store.code,
  //         });
  //       });

  //       setSelectedStoreMap(map);

  //       setTotalCount(response.data.total_count);

  //       return ids;
  //     }
  //     return [];
  //   } catch (error) {
  //     console.error("Error fetching all store IDs:", error);
  //     return [];
  //   }
  // };
  const fetchAllStoreIds = async (): Promise<number[]> => {
    let page = 1;
    const size = 100;
    let allIds: number[] = [];
    const map = new Map<number, StoreDataProps>();
    let hasMore = true;

    while (hasMore) {
      const response = await axiosInstance.get<Stories>("api/stores/", {
        params: {
          page,
          page_size: size,
          ...memoizedFilters,
        },
      });

      const stores = response.data.objects;

      if (stores && stores.length > 0) {
        const ids = stores.map((store) => store.id);
        allIds = [...allIds, ...ids];

        stores.forEach((store) => {
          map.set(store.id, {
            id: store.id,
            name: store.name,
            code: store.code,
          });
        });

        if (stores.length < size) {
          hasMore = false;
        } else {
          page += 1;
        }
      } else {
        hasMore = false;
      }
    }

    setSelectedStoreMap(map); // update for tag display
    return allIds;
  };

  // Fetch stores for current page
  const fetchStores = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get<Stories>(`api/stores/`, {
        params: {
          page: currentPage,
          page_size: pageSize,
          ...memoizedFilters,
        },
      });

      if (response.data?.objects) {
        const fetchedStores = response.data.objects.map(
          (store: StoreDataProps) => ({
            id: store.id,
            name: store.name,
            code: store.code,
          })
        );

        setStoreOptions(fetchedStores);
        setTotalCount(response.data.total_count);

        const initiallySelected = new Set(
          fetchedStores
            .filter((store: StoreDataProps) => store.is_active)
            .map((store: any) => store.id)
        );

        setInitialSelectedStores(initiallySelected);

        if (isDeselectingAll) {
          return;
        }

        if (selectedStores.size === 0 && newlySelectedStores.size === 0) {
          setSelectedStores(
            new Set([...initiallySelected, ...newlySelectedStores])
          );
        } else {
          setSelectedStores((prevSelected) => {
            const updatedSelection = new Set(prevSelected);
            initiallySelected.forEach((id) => updatedSelection.add(id));
            return updatedSelection;
          });
        }
      } else {
        setStoreOptions([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching stores:", error);
      notification.error({
        message: "Error",
        description: "Failed to load stores.",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch stores on mount, pagination, or filter change
  useEffect(() => {
    fetchStores();
  }, [currentPage, pageSize, memoizedFilters]);

  // Effect: Uncheck Select All if a deselect happens after Select All
  useEffect(() => {
    if (selectAllActive && selectedStores.size < totalCount) {
      setIsAllSelected(false);
      setSelectAllActive(false);
    }
  }, [selectedStores, totalCount, selectAllActive]);

  // Table row selection logic
  const rowSelection: TableRowSelection<StoreDataProps> = {
    selectedRowKeys: Array.from(selectedStores),
    // Key trick: force DataTable to re-render when selectAll state changes
    // key: isAllSelected ? 'selectAll' : 'notSelectAll',
    onChange: (selectedRowKeys: React.Key[]) => {
      console.log("selectedRowKeys", selectedRowKeys);
      if (isDeselectingAll) return;

      const currentPageStoreIds = storeOptions.map((store) => store.id);
      const selectionsFromOtherPages = Array.from(selectedStores).filter(
        (storeId) => !currentPageStoreIds.includes(storeId)
      );
      const newSelectedKeys = selectedRowKeys.map(Number);
      const allSelections = [...selectionsFromOtherPages, ...newSelectedKeys];

      // If selectAll was active, but user deselected an item, uncheck selectAll
      if (selectAllActive && newSelectedKeys.length !== storeOptions.length) {
        console.log("deselected");
        setIsAllSelected(false);
        setSelectAllActive(false);
      }

      setSelectedStores(new Set(allSelections));
    },
    onSelect: (record: any, selected: boolean) => {
      console.log("selected record", record);
      console.log("selected", selected);
      setSelectedStores((prev) => {
        const newSet = new Set(prev);

        if (selected) {
          newSet.add(record.id);
          setNewlySelectedStores((prev) => new Set(prev).add(record.id));
          setIsAllSelected(true);
          setSelectAllActive(true);
        } else {
          newSet.delete(record.id);
          setNewlySelectedStores((prev) => {
            const updatedNewlySelected = new Set(prev);
            updatedNewlySelected.delete(record.id);
            return updatedNewlySelected;
          });

          if (selectAllActive) {
            setIsAllSelected(true);
            setSelectAllActive(true);
          }
        }
        return newSet;
      });
      setSelectedStoreMap((prevMap) => {
        const updated = new Map(prevMap);
        updated.set(record.id, {
          id: record.id,
          name: record.name,
          code: record.code,
        });
        return updated;
      });
    },
    onSelectAll: async (selected, selectedRows, changeRows) => {
      if (selected) {
        try {
          setLoading(true);
          const allStoreIds = await fetchAllStoreIds();
          setSelectedStores(new Set(allStoreIds));
          setNewlySelectedStores(new Set(allStoreIds));
          setIsAllSelected(true);
          setSelectAllActive(true);
          message.success(
            `Selected ${allStoreIds.length} stores across all pages`
          );
        } catch (error) {
          console.error("Error selecting all stores:", error);
          message.error("Failed to select all stores");
          void selectedRows;
          setSelectedStores((prevSelected) => {
            const updatedSelection = new Set(prevSelected);
            changeRows.forEach((store) => updatedSelection.add(store.id));
            setNewlySelectedStores((prev) => {
              const updatedNewlySelected = new Set(prev);
              changeRows.forEach((store) => {
                if (!initialSelectedStores.has(store.id)) {
                  updatedNewlySelected.add(store.id);
                }
              });
              return updatedNewlySelected;
            });
            return updatedSelection;
          });
        } finally {
          setLoading(false);
        }
      } else {
        setIsDeselectingAll(true);
        setSelectedStores(() => new Set());
        setNewlySelectedStores(() => new Set());
        setIsAllSelected(false);
        setSelectAllActive(false);
        message.success("Deselected all stores from all pages");
        setTimeout(() => {
          setIsDeselectingAll(false);
          if (selectedStores.size > 0) {
            setSelectedStores(new Set());
            setNewlySelectedStores(new Set());
          }
        }, 200);
      }
    },
    columnWidth: "5%",
  };

  // Handle Save
  const handleStoreMapping = async () => {
    if (selectedStores.size === 0) {
      message.error("Please select at least one store.");
      return;
    }
    const finalStoreIds = Array.from(selectedStores);
    const payload = { stores: finalStoreIds };
    try {
      setIsSaving(true);
      const response = await axiosInstance.post(
        "api/menu/push-menu-to-multiple-stores/",
        payload
      );
      if (response.status === 201 || response.status === 200) {
        message.success(`${response.data.message}`);
        setSelectedStores(new Set());
        setNewlySelectedStores(new Set());
      }
    } catch (error) {
      setIsSaving(false);
      console.error("Error mapping stores:", error);
      let errorMessage = "Failed to update stores.";
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 400) {
          errorMessage = "Please Select at least one store.";
        } else {
          errorMessage =
            error.response?.data?.message ||
            error.response?.data?.error ||
            JSON.stringify(error.response?.data) ||
            errorMessage;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      setIsSaving(false);
      message.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  // Filter, pagination, and utility handlers
  const handleSearchChange = (value: string) => {
    handleFilterChange("search", value);
  };

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search") {
      setSearchValue("");
    }
  };
  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    setPageSize(pageSize || 999);
  };
  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";
    return text
      .replace(/[^a-zA-Z0-9\/\- ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  // Remove store from tags
  const handleRemoveStore = (id: number) => {
    setSelectedStores((prev) => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });

    setNewlySelectedStores((prev) => {
      const updated = new Set(prev);
      updated.delete(id);
      return updated;
    });

    if (selectAllActive) {
      setIsAllSelected(false);
      setSelectAllActive(false);
    }
  };

  const columns = [
    {
      title: isAllSelected ? "Deselect All" : "Select All",
      dataIndex: "",
      key: "",
      width: "10%",
      fixed: "left" as "left",
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "40%",
      render: (text: string, record: any) =>
        text ? (
          <Link
            className="common-link text-decoration-none"
            to={`/stores/${record.id}`}
          >
            {text}
          </Link>
        ) : (
          "N/A"
        ),
    },
    { title: "Code", dataIndex: "code", key: "code", width: "30%" },
  ];

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div className="header products-headers">
          <div className="title">Single Menu Push to Stores</div>
        </div>
      </div>
      <div className="d-flex flex-wrap justify-content-between align-items-center mb-3 mt-4">
        <div className="d-flex flex-wrap align-items-center">
          <div className="search-btn-driver">
            <SearchBox
              placeholder="Search by code & name"
              value={searchValue}
              onChange={setSearchValue}
              onSearch={() => handleSearchChange(searchValue)}
            />
          </div>
          <div>
            <FilterButtons
              showClearButtons={showClearButtons}
              appliedFilters={appliedFilters}
              clearFilter={clearFilterHandler}
              formatFilterValue={formatPaymentMethod}
              filters={filters}
            />
          </div>
          {selectedStores.size > 0 && (
            <>
              <div className="ml-3 text-primary">
                <strong>{`${selectedStores.size} store(s) selected`}</strong>
              </div>
            </>
          )}
        </div>
        <div className="ml-3">
          <Button
            type="primary"
            className="btn-save"
            onClick={handleStoreMapping}
          >
            {isSaving ? `Saving...` : `Generate Menu & Push to Stores`}
          </Button>
        </div>
      </div>
      <div>
        <Alert
          className="alert alert-warning "
          message={WARNING_MESSAGE}
          type="warning"
        />
      </div>
      <>
        <Card className="mb-4 selected-store-card">
          <div className="d-flex flex-wrap gap-2">
            {Array.from(selectedStores).map((storeId) => {
              const store = selectedStoreMap.get(storeId);
              return (
                <Tag
                  className="family-font-Poppins fs-6 cursor-pointer"
                  key={storeId}
                  closable
                  onClick={() => handleRemoveStore(storeId)}
                  closeIcon={<CloseOutlined />}
                >
                  {store?.name || `Store ${storeId}`}
                </Tag>
              );
            })}
          </div>
        </Card>
        <div className="mt-3">
          <DataTable
            rowSelection={rowSelection}
            dataSource={storeOptions}
            columns={columns}
            loading={loading}
            rowKey="id"
            pagination={false}
            scroll={{ x: "max-content" }}
            key={isAllSelected ? "selectAll" : "notSelectAll"}
          />
        </div>
        <div className="d-flex justify-content-end mt-3 mb-3">
          <CommonPagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            onChange={handlePageChange}
            showSizeChanger
          />
        </div>
      </>
    </div>
  );
};

export default SingleMenuPushToStores;
