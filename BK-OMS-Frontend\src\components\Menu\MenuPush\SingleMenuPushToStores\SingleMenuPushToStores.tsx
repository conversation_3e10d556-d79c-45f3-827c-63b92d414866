import React, { useEffect, useMemo, useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  message,
  notification,
  Tag,
  Checkbox,
  Upload,
  Tooltip,
} from "antd";
import { CloseOutlined, UploadOutlined, DownloadOutlined } from "@ant-design/icons";
import type { UploadProps } from "antd";
import { Link } from "react-router-dom";
import { axiosInstance } from "../../../../apiCalls";
import axios from "axios";
import { useTableFilters } from "../../../../customHooks/useFilter";
import FilterButtons from "../../../UI/FilterButton";
import DataTable from "../../../UI/DataTable/DataTable";
import CommonPagination from "../../../UI/Pagination/commonPagination";
import SearchBox from "../../../UI/SearchBox";
import { StoreDataProps } from "../../../../types";
import { Stories } from "../../../Stores/StoreList";
import {
  ALERT_MESSAGE,
  BULK_MENU_PUSH_TO_STORES,
  CLEARE_ALL,
  DESELECT,
  GENERATE_MENU_PUSH_STORES,
  S,
  SAVING,
  SELECT_ALL,
  SELECTED,
  STORE,
} from "../Text/Contants";

const SingleMenuPushToStores: React.FC = () => {
  const {
    filters,
    appliedFilters,
    showClearButtons,
    handleFilterChange,
    clearFilter,
  } = useTableFilters();

  // Core state
  const [storeOptions, setStoreOptions] = useState<StoreDataProps[]>([]);
  const [selectedStores, setSelectedStores] = useState<Set<number>>(new Set());
  const [newlySelectedStores, setNewlySelectedStores] = useState<Set<number>>(new Set());
  const [selectedStoreMap, setSelectedStoreMap] = useState<
    Map<number, StoreDataProps>
  >(new Map());

  // UI state
  const [loading, setLoading] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);

  // Search state
  const [searchValue, setSearchValue] = useState<string>("");

  // CSV Upload state
  const [csvUploading, setCsvUploading] = useState<boolean>(false);

  // Memoized filters to prevent unnecessary re-renders
  const memoizedFilters = useMemo(() => filters, [filters]);

  // Computed state for select all functionality
  const isAllSelected = useMemo(() => {
    return totalCount > 0 && selectedStores.size === totalCount;
  }, [selectedStores.size, totalCount]);

  // CSV parsing function
  const parseCSV = (csvText: string): Array<{storeName: string, storeCode: string}> => {
    const lines = csvText.split('\n').filter(line => line.trim());
    if (lines.length === 0) return [];

    // Get headers (first line)
    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    const storeNameIndex = headers.findIndex(h => h.includes('store') && h.includes('name'));
    const storeCodeIndex = headers.findIndex(h => h.includes('store') && h.includes('code'));

    if (storeNameIndex === -1 || storeCodeIndex === -1) {
      throw new Error('CSV must contain "Store Name" and "Store Code" columns');
    }

    // Parse data rows
    const data: Array<{storeName: string, storeCode: string}> = [];
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      if (values.length > Math.max(storeNameIndex, storeCodeIndex)) {
        const storeName = values[storeNameIndex]?.replace(/"/g, '') || '';
        const storeCode = values[storeCodeIndex]?.replace(/"/g, '') || '';
        if (storeName && storeCode) {
          data.push({ storeName, storeCode });
        }
      }
    }
    return data;
  };

  // Function to select stores based on CSV store codes
  const selectStoresFromCSV = async (csvStoreCodes: string[]) => {
    try {
      setCsvUploading(true);

      // Get all stores to match against CSV codes
      const allStoresMap = await fetchAllStoresMap();

      // Find matching stores by code
      const matchingStoreIds: number[] = [];
      const notFoundCodes: string[] = [];

      csvStoreCodes.forEach(code => {
        const matchingStore = Array.from(allStoresMap.values()).find(
          store => store.code?.toLowerCase() === code.toLowerCase()
        );
        if (matchingStore) {
          matchingStoreIds.push(matchingStore.id);
        } else {
          notFoundCodes.push(code);
        }
      });

      // Update selections
      setSelectedStores(new Set(matchingStoreIds));
      setNewlySelectedStores(new Set(matchingStoreIds));

      // Update store map for selected stores
      const selectedStoreMap = new Map<number, StoreDataProps>();
      matchingStoreIds.forEach(id => {
        const store = allStoresMap.get(id);
        if (store) {
          selectedStoreMap.set(id, store);
        }
      });
      setSelectedStoreMap(selectedStoreMap);

      // Show results
      const successMessage = `Selected ${matchingStoreIds.length} stores from CSV`;
      if (notFoundCodes.length > 0) {
        message.warning(`${successMessage}. ${notFoundCodes.length} store codes not found: ${notFoundCodes.slice(0, 5).join(', ')}${notFoundCodes.length > 5 ? '...' : ''}`);
      } else {
        message.success(successMessage);
      }

    } catch (error) {
      console.error('Error selecting stores from CSV:', error);
      message.error('Failed to process CSV data');
    } finally {
      setCsvUploading(false);
    }
  };

  // Fetch all store IDs for Select All
  const fetchAllStoreIds = async (): Promise<number[]> => {
    let page = 1;
    const size = 100;
    let allIds: number[] = [];
    const map = new Map<number, StoreDataProps>();
    let hasMore = true;

    while (hasMore) {
      const response = await axiosInstance.get<Stories>("api/stores/", {
        params: {
          page,
          page_size: size,
          ...memoizedFilters,
        },
      });

      const stores = response.data.objects;

      if (stores && stores.length > 0) {
        const ids = stores.map((store) => store.id);
        allIds = [...allIds, ...ids];

        stores.forEach((store) => {
          map.set(store.id, {
            id: store.id,
            name: store.name,
            code: store.code,
          });
        });

        if (stores.length < size) {
          hasMore = false;
        } else {
          page += 1;
        }
      } else {
        hasMore = false;
      }
    }

    setSelectedStoreMap(map); // update for tag display
    return allIds;
  };

  // Fetch all stores with their details for CSV matching
  const fetchAllStoresMap = async (): Promise<Map<number, StoreDataProps>> => {
    let page = 1;
    const size = 100;
    const map = new Map<number, StoreDataProps>();
    let hasMore = true;

    while (hasMore) {
      try {
        const response = await axiosInstance.get<Stories>(`api/stores/`, {
          params: {
            page,
            page_size: size,
            ...memoizedFilters,
          },
        });

        const stores = response.data?.objects || [];
        stores.forEach((store: StoreDataProps) => {
          map.set(store.id, store);
        });

        hasMore = response.data?.next_page !== null;
        page++;
      } catch (error) {
        console.error(`Error fetching stores page ${page}:`, error);
        break;
      }
    }

    return map;
  };

  // CSV upload handler
  const handleCSVUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csvText = e.target?.result as string;
        const parsedData = parseCSV(csvText);

        if (parsedData.length === 0) {
          message.warning('No valid data found in CSV file');
          return;
        }

        // Extract store codes and select stores
        const storeCodes = parsedData.map(item => item.storeCode);
        selectStoresFromCSV(storeCodes);

      } catch (error) {
        console.error('Error parsing CSV:', error);
        message.error(error instanceof Error ? error.message : 'Failed to parse CSV file');
      }
    };

    reader.onerror = () => {
      message.error('Failed to read CSV file');
    };

    reader.readAsText(file);
    return false; // Prevent default upload behavior
  };

  // Download sample CSV function
  const downloadSampleCSV = () => {
    const sampleData = [
      ['Store Name', 'Store Code'],
      ['Sample Store 1', 'STORE001'],
      ['Sample Store 2', 'STORE002'],
      ['Sample Store 3', 'STORE003']
    ];

    const csvContent = sampleData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', 'sample_stores.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Upload props for Ant Design Upload component
  const uploadProps: UploadProps = {
    name: 'csvFile',
    accept: '.csv',
    showUploadList: false,
    beforeUpload: handleCSVUpload,
    disabled: csvUploading,
  };

  // Fetch stores for current page
  const fetchStores = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get<Stories>(`api/stores/`, {
        params: {
          page: currentPage,
          page_size: pageSize,
          ...memoizedFilters,
        },
      });

      if (response.data?.objects) {
        const fetchedStores = response.data.objects.map(
          (store: StoreDataProps) => ({
            id: store.id,
            name: store.name,
            code: store.code,
          })
        );

        setStoreOptions(fetchedStores);
        setTotalCount(response.data.total_count);

        // Update store map for the current page, but preserve already selected
        setSelectedStoreMap((prev) => {
          const updated = new Map(prev);
          fetchedStores.forEach((store) => {
            if (!updated.has(store.id)) {
              updated.set(store.id, store);
            }
          });
          return updated;
        });
      } else {
        setStoreOptions([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching stores:", error);
      notification.error({
        message: "Error",
        description: "Failed to load stores.",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch stores on mount, pagination, or filter change
  useEffect(() => {
    fetchStores();
    // eslint-disable-next-line
  }, [currentPage, pageSize, memoizedFilters]);

  // Cleanup function to prevent memory leaks
  useEffect(() => {
    return () => {
      setSelectedStores(new Set());
      setNewlySelectedStores(new Set());
      setSelectedStoreMap(new Map());
    };
  }, []);

  // Handle single row checkbox change
  const handleRowCheckboxChange = (store: StoreDataProps, checked: boolean) => {
    setSelectedStores((prev) => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(store.id);
        setSelectedStoreMap((prevMap) => {
          const updated = new Map(prevMap);
          updated.set(store.id, store);
          return updated;
        });
      } else {
        newSet.delete(store.id);
      }
      return newSet;
    });
  };

  // Handle "Select All" checkbox in header
  const handleSelectAllCheckbox = async (checked: boolean) => {
    if (checked) {
      setLoading(true);
      try {
        const allStoreIds = await fetchAllStoreIds();
        setSelectedStores(new Set(allStoreIds));
        message.success(
          `Selected ${allStoreIds.length} stores across all pages`
        );
      } catch (error) {
        message.error("Failed to select all stores");
      } finally {
        setLoading(false);
      }
    } else {
      setSelectedStores(new Set());
      message.success("Deselected all stores");
    }
  };

  // Remove store from tags
  const handleRemoveStore = (id: number) => {
    setSelectedStores((prev) => {
      const newSet = new Set(prev);
      newSet.delete(id);
      return newSet;
    });
  };

  // Handle Save
  const handleStoreMapping = async () => {
    if (selectedStores.size === 0) {
      message.error("Please select at least one store.");
      return;
    }
    const finalStoreIds = Array.from(selectedStores);
    const payload = { stores: finalStoreIds };
    try {
      setIsSaving(true);
      const response = await axiosInstance.post(
        "api/menu/push-menu-to-multiple-stores/",
        payload
      );
      if (response.status === 201 || response.status === 200) {
        message.success(`${response.data.message}`);
        setSelectedStores(new Set());
      }
    } catch (error) {
      setIsSaving(false);
      console.error("Error mapping stores:", error);
      let errorMessage = "Failed to update stores.";
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 400) {
          errorMessage = "Please Select at least one store.";
        } else {
          errorMessage =
            error.response?.data?.message ||
            error.response?.data?.error ||
            JSON.stringify(error.response?.data) ||
            errorMessage;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      setIsSaving(false);
      message.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  // Filter, pagination, and utility handlers
  const handleSearchChange = (value: string) => {
    handleFilterChange("search", value);
  };

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search") {
      setSearchValue("");
    }
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    setPageSize(pageSize || 999);
  };

  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";
    return text
      .replace(/[^a-zA-Z0-9\/\- ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  // Table columns (no rowSelection)
  const columns = [
    {
      title: (
        <Checkbox
          checked={isAllSelected}
          indeterminate={
            selectedStores.size > 0 && selectedStores.size < totalCount
          }
          onChange={(e) => handleSelectAllCheckbox(e.target.checked)}
        >
          {isAllSelected ? `${DESELECT}` : `${SELECT_ALL}`}
        </Checkbox>
      ),
      dataIndex: "checkbox",
      key: "checkbox",
      width: "10%",
      fixed: "left" as "left",
      render: (_: any, record: StoreDataProps) => (
        <Checkbox
          checked={selectedStores.has(record.id)}
          onChange={(e) => handleRowCheckboxChange(record, e.target.checked)}
        />
      ),
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "40%",
      render: (text: string, record: any) =>
        text ? (
          <Link
            className="common-link text-decoration-none"
            to={`/stores/${record.id}`}
          >
            {text}
          </Link>
        ) : (
          "N/A"
        ),
    },
    { title: "Code", dataIndex: "code", key: "code", width: "30%" },
  ];

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div className="header products-headers">
          <div className="title">{BULK_MENU_PUSH_TO_STORES}</div>
        </div>
      </div>
      <div className="d-flex flex-wrap justify-content-between align-items-center mb-3 mt-4">
        <div className="d-flex flex-wrap align-items-center">
          <div className="search-btn-driver">
            <SearchBox
              placeholder="Search by code & name"
              value={searchValue}
              onChange={setSearchValue}
              onSearch={() => handleSearchChange(searchValue)}
            />
          </div>
          <div>
            <FilterButtons
              showClearButtons={showClearButtons}
              appliedFilters={appliedFilters}
              clearFilter={clearFilterHandler}
              formatFilterValue={formatPaymentMethod}
              filters={filters}
            />
          </div>
          
          {selectedStores.size > 0 && (
            <div className="ml-3">
              <Tag
                className="badge  px- py-2 fs-6 fw-bold font-family-Poppins rounded-pill"
                color="blue"
              >
                ({selectedStores.size}) {STORE}
                {selectedStores.size > 1 ? `${S}` : ``} {SELECTED}
              </Tag>
            </div>
          )}
        </div>
        <div className="d-flex align-items-center">
          <div className="ml-3 d-flex align-items-center">
            <Tooltip title="Upload CSV file with Store Name and Store Code columns to auto-select stores">
              <Upload {...uploadProps}>
                <Button
                  icon={<UploadOutlined />}
                  loading={csvUploading}
                  disabled={csvUploading}
                  size="small"
                >
                  {csvUploading ? 'Processing...' : 'Upload CSV'}
                </Button>
              </Upload>
            </Tooltip>
            <Tooltip title="Download sample CSV format">
              <Button
                icon={<DownloadOutlined />}
                onClick={downloadSampleCSV}
                size="small"
                type="link"
                className="ml-2"
              >
                Sample
              </Button>
            </Tooltip>
          </div>
          <Button
            type="primary"
            className="btn-save"
            onClick={handleStoreMapping}
          >
            {isSaving ? `${SAVING}` : `${GENERATE_MENU_PUSH_STORES}`}
          </Button>
        </div>
      </div>
      <div>
        <div className="container-fluid">
          <div className="row">
            <div className="col-12">
              <div className="alert alert-warning rounded-pill text-center fw-semibold px-4 py-2 mb-3">
                {ALERT_MESSAGE}
              </div>
            </div>
          </div>
        </div>
      </div>
      <>
        <Card className="mb-4 selected-store-card">
          <div className="d-flex flex-wrap gap-2">
            {selectedStores.size > 0 && (
              <>
                <Button
                  className="btn btn-light btn-sm px-2 py-1 rounded-pill border"
                  onClick={() => setSelectedStores(new Set())}
                >
                  <span className="text-danger fw-bold fs-6">{CLEARE_ALL}</span>
                </Button>
              </>
            )}
            {Array.from(selectedStores).map((storeId) => {
              const store = selectedStoreMap.get(storeId);
              return (
                <Tag
                  className="family-font-Poppins fs-6 cursor-pointer"
                  key={storeId}
                  closable
                  onClick={() => handleRemoveStore(storeId)}
                  closeIcon={<CloseOutlined />}
                >
                  {store?.name && store?.code
                    ? `${store.name} (${store.code})`
                    : `${STORE} ${storeId}`}
                </Tag>
              );
            })}
          </div>
        </Card>
        <div className="mt-3">
          <DataTable
            dataSource={storeOptions}
            columns={columns}
            loading={loading}
            rowKey="id"
            pagination={false}
            scroll={{ x: "max-content" }}
          />
        </div>
        <div className="d-flex justify-content-end mt-3 mb-3">
          <CommonPagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            onChange={handlePageChange}
            showSizeChanger
          />
        </div>
      </>
    </div>
  );
};

export default SingleMenuPushToStores;
