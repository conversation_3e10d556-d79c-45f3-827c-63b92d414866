import { useEffect, useState } from "react";
import { Form, InputNumber, Button, Select, message } from "antd";
import { axiosInstance } from "../../../apiCalls";
import { useNavigate, useParams } from "react-router-dom";
import "../addStore/AddStore.css";

interface AddStoreCategoryProps {
  store: number;
  category: number;
  is_active: boolean;
  position: number;
}

interface ListCategoriesProps {
  id: number;
  name: string;
}

const AddStoreCategory = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [listCategories, setListCategories] = useState<ListCategoriesProps[]>(
    []
  );
  const [loading, setLoading] = useState(false);
  const [storeCategory, setStoreCategory] = useState<AddStoreCategoryProps>({
    store: Number(id),
    category: 0,
    is_active: true,
    position: 0,
  });

  const onValuesChange = (
    _: Partial<AddStoreCategoryProps>,
    allValues: AddStoreCategoryProps
  ) => {
    setStoreCategory({ ...allValues, store: Number(id) });
  };

  const onFinish = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.post(
        `api/menu/store-categories/`,
        storeCategory
      );
      if (response.status === 201) {
        message.success("Store Category Created Successfully!");
        navigate(`/stores/${id}`);
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error response from API:", error.response.data);
        message.error(
          `Failed to create Store Category: ${
            error.response.data.message || "Unknown error"
          }`
        );
      } else {
        console.error("Error creating Store Category:", error);
        message.error("Failed to Create Store Category.");
      }
    }
    setLoading(false);
  };

  const fetchCategoriesList = async () => {
    try {
      const response = await axiosInstance.get(`api/menu/categories/`);
      if (response.status === 200) {
        setListCategories(response.data.objects);
      } else {
        message.error("Error in fetching categories");
        navigate("/stores");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error response from API:", error.response.data);
        message.error(
          `Failed to fetch: ${error.response.data.message || "Unknown error"}`
        );
      } else {
        console.error("Error fetch:", error);
        message.error("Failed Fetch.");
      }
    }
  };

  useEffect(() => {
    fetchCategoriesList();
  }, []);

  return (
    <div>
      <h3>Add Store Category</h3>
      <Form
        form={form}
        name="add_store_category"
        layout="vertical"
        onFinish={onFinish}
        onValuesChange={onValuesChange}
        initialValues={{ ...storeCategory, category: "" }}
        className="add-store-form"
      >
        <Form.Item
          name="category"
          label="Category"
          rules={[{ required: true, message: "Please select Category" }]}
          className="form-item"
        >
          <Select className="input-field">
            {listCategories?.map((category) => (
              <Select.Option key={category.id} value={category.id}>
                {category.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="is_active"
          label="Active"
          rules={[{ required: true, message: "Please select active status" }]}
          className="form-item"
        >
          <Select className="input-field">
            <Select.Option value={true}>Active</Select.Option>
            <Select.Option value={false}>Inactive</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="position"
          label="Position"
          rules={[{ required: true, message: "Please enter position" }]}
          className="form-item"
        >
          <InputNumber min={0} max={100} className="input-field" />
        </Form.Item>

        <Form.Item className="form-item">
          <Button
            type="primary"
            htmlType="submit"
            className="submit-button"
            loading={loading}
          >
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddStoreCategory;
