import React, { useEffect, useState } from "react";
import { Form, Input, Button, message, Select, Popover } from "antd";
import { useNavigate } from "react-router-dom";
import "../Stores/addStore/AddStore.css";
import { axiosInstance } from "../../apiCalls";

interface AddModifierProps {
  is_active: boolean;
  product_variant: number;
}

const AddModifier: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [productId, setProductId] = useState<number>(0);
  const [storeSearch, setStoreSearch] = useState("");
  const [storeResults, setStoreResults] = useState<
    { id: number; display_name: string; base_product: number }[]
  >([]);
  const [storePopoverVisible, setStorePopoverVisible] = useState(false);

  const onValuesChange = (_: Partial<AddModifierProps>, _allValues: AddModifierProps) => {
    form.setFieldsValue({ product_variant: productId });
  };

  /** Fetch Stores from API */
  const fetchStores = async () => {
    try {
      const response = await axiosInstance.get(
        `api/menu/v2/search-product-variants/?search=${storeSearch}`
      );
      if (response.status === 200) {
        setStoreResults(
          response.data.map((store: any) => ({
            id: store.id,
            display_name: store.display_name,
            base_product: store.base_product,
          }))
        );
      }
    } catch (error) {
      console.error("Error fetching stores", error);
    }
  };

  useEffect(() => {
    form.setFieldsValue({ product_variant: productId });
  }, [productId, form]);

  useEffect(() => {
    if (storeSearch.length >= 3 && storePopoverVisible) {
      fetchStores();
    }
  }, [storeSearch, storePopoverVisible]);

  const onFinish = async (values: AddModifierProps) => {
    try {
      const response = await axiosInstance.post(`api/menu/modifiers/`, values);
      if (response.status === 201) {
        message.success("Modifier Created Successfully!");
        navigate("/modifiers");
      }
    } catch (error: any) {
      if (error.response) {
        message.error(`Failed to create modifier: ${error.response.data.message || "Unknown error"}`);
      } else {
        message.error("Failed to create modifier.");
      }
    }
  };

  const storePopoverContent = (
    <div>
      {storeResults.map((store) => (
        <div
          key={store.id}
          onClick={() => {
            setProductId(store.id);
            setStoreSearch(store.display_name);
            setStorePopoverVisible(false);
            form.setFieldsValue({ product_variant: store.id });
            form.validateFields(["product_variant"]).catch(() => {});
          }}
          style={{ cursor: "pointer", padding: "5px" }}
        >
          {store.display_name}
        </div>
      ))}
    </div>
  );

  return (
    <div>
      <h3>Add Modifier</h3>
      <Form
        form={form}
        name="add_modifier"
        layout="vertical"
        onFinish={onFinish}
        onValuesChange={onValuesChange}
        initialValues={{ is_active: true, product_variant: productId }}
        className="add-store-form"
      >
        {/* Product Variant */}
        <Form.Item
          name="product_variant"
          label="Product Variant"
          rules={[{ required: true, message: "Please enter a product" }]}
          className="form-item"
        >
          <Popover
            content={storePopoverContent}
            title="Search Results"
            trigger="click"
            placement="bottom"
            open={storeSearch.length >= 3 && storeResults.length > 0 && storePopoverVisible}
            onOpenChange={(open) => setStorePopoverVisible(open)}
          >
            <Input
              placeholder="Search Product"
              value={storeSearch}
              onChange={(e) => {
                setStoreSearch(e.target.value);
                setStorePopoverVisible(true);
              }}
              onClick={() => setStorePopoverVisible(true)}
            />
          </Popover>
        </Form.Item>

        {/* Status */}
        <Form.Item name="is_active" label="Status" className="form-item">
          <Select
            onChange={(value) => form.setFieldsValue({ is_active: value })}
            defaultValue={true}
          >
            <Select.Option value={true}>Active</Select.Option>
            <Select.Option value={false}>Inactive</Select.Option>
          </Select>
        </Form.Item>

        {/* Submit Button */}
        <Form.Item className="form-item">
          <Button type="primary" htmlType="submit" className="submit-button">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddModifier;
