import React, { useCallback, useEffect, useState } from "react";
import { Form, Input, Button, message, Switch, Spin } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import { axiosInstance } from "../../../../apiCalls";
import { StoreGroup } from "../StoreGroupsList/StoreGroupsList";
import BackButton from "../../../UI/BackButton";

const EditStoreGroup: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [loadingData, setLoadingData] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [storeGroup, setStoreGroup] = useState<StoreGroup | undefined>(
    undefined
  );
  const [initialValues, setInitialValues] = useState<Record<string, any>>({});

  useEffect(() => {
    const fetchStoreGroup = async () => {
      try {
        const response = await axiosInstance.get(`/api/stores/groups/${id}/`);
        const data = response.data;
        setInitialValues(data);
        setStoreGroup(data);
        form.setFieldsValue(data);
      } catch (error: any) {
        console.error("Error fetching store group:", error);
        message.error("Failed to fetch store group details.");
      } finally {
        setLoadingData(false);
      }
    };

    fetchStoreGroup();
  }, [id, form]);

  const handleSubmit = useCallback(
    async (values: any) => {
      setSubmitting(true);
      try {
        const updatedValues = Object.keys(values).reduce((acc, key) => {
          if (
            values[key as keyof any] !== initialValues[key as keyof StoreGroup]
          ) {
            acc[key] = values[key];
          }
          return acc;
        }, {} as Partial<any>);

        // If no changes detected, show a message and return
        if (Object.keys(updatedValues).length === 0) {
          message.info("No changes detected.");
          setSubmitting(false);
          return;
        }

        const response = await axiosInstance.patch(
          `/api/stores/groups/${id}/`,
          values
        );

        if (response.status === 200) {
          message.success("Store group updated successfully!");
          navigate(`/store-group-details/${id}`);
        }
      } catch (error: any) {
        console.error("Error updating store group:", error);
        const errorMessage =
          error.response?.data?.message || "Failed to update store group.";
        message.error(errorMessage);
      } finally {
        setSubmitting(false);
      }
    },
    [id, navigate]
  );

  if (loadingData) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <BackButton to={`/store-group-details/${id}`} />
      <div className="p-4">
        <h2 className="text-xl font-semibold mb-4">Edit Store Group</h2>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={storeGroup}
          className="add-store-form"
        >
          <Form.Item
            label="Name"
            name="name"
            rules={[{ required: true, message: "Please enter the store name" }]}
          >
            <Input placeholder="Enter Store Group Name" />
          </Form.Item>

          <Form.Item
            label="Description"
            name="description"
            rules={[
              { required: true, message: "Please enter the description" },
              { max: 255, message: "Description cannot exceed 255 characters" },
            ]}
          >
            <Input.TextArea rows={3} placeholder="Enter Description" />
          </Form.Item>

          <Form.Item
            label="Active Status"
            name="is_active"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item>
            <Button
              className="btn-save"
              type="primary"
              htmlType="submit"
              loading={submitting}
              block
            >
              Update Store Group
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default EditStoreGroup;
