import { useCallback, useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Typo<PERSON>, Button, message } from "antd";
import DataTable from "../../../UI/DataTable/DataTable";
import { axiosInstance } from "../../../../apiCalls";
import "../../../Products/Products.css";

// const { Link } = Typography;

export interface GroupMappedStore {
  id: number;
  group_name: string;
  description: string;
  store_code: string;
  store_name: string;
  is_active: boolean;
  group: number;
  store: number;
}

const GroupMappedStores: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [storeGroups, setStoreGroups] = useState<GroupMappedStore[]>([]);
  const [loading, setLoading] = useState(false);
  //   const [search, setSearch] = useState("");

  const fetchStoreGroups = useCallback(async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/stores/groups-stores/?group_id=${id}`
      );
      setStoreGroups(response.data || []);
    } catch (error) {
      console.error("Error fetching store groups:", error);
      message.error("Failed to fetch store groups. Please try again.");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStoreGroups();
  }, [fetchStoreGroups]);

  const handleAddNew = useCallback(() => {
    navigate(`/map-group-to-stores/${id}`);
  }, [navigate]);

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      render: (text: string) => (
        <span>
          <Typography>{text}</Typography>
        </span>
      ),
    },
    {
      title: "Group Name",
      dataIndex: "group_name",
      key: "group_name",
    },
    {
      title: "Store Name",
      dataIndex: "store_name",
      key: "store_name",
    },
    {
      title: "Store Code",
      dataIndex: "store_code",
      key: "store_code",
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "status",
      width: "20%",
      render: (is_active: boolean) => (
        <div className="d-flex">
          <span className="ml-2">{is_active ? "Yes" : "No"}</span>
        </div>
      ),
    },
  ];

  return (
    <div>
      <div className="main-dashboard-buttons mt-3">
        <Button className="typography" type="primary" onClick={handleAddNew}>
          Map Group to Stores
        </Button>
      </div>

      <div className="store-header">
        {/* <h2>Store Groups</h2> */}
        {/* <SearchBox
          value={search}
          onChange={setSearch}
          onSearch={() => console.log("searching", search)}
          onClear={() => setSearch("")}
          placeholder="Enter Code or Name"
        /> */}
      </div>

      <DataTable<GroupMappedStore>
        columns={columns}
        dataSource={storeGroups}
        loading={loading}
        pagination={false}
        scroll={{ x: "max-content" }}
      />
    </div>
  );
};

export default GroupMappedStores;
