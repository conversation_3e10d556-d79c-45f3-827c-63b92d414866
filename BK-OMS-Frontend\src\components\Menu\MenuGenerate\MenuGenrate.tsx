import React, { useEffect, useState } from "react";
import {  Input, message, Select, Spin } from "antd";
import type { SelectProps } from "antd";
import { axiosInstance } from "../../../apiCalls";

interface StoreDataProps {
  id: number;
  name: string;
  code: string;
  third_party_id: string;
}

const MenuGenerate: React.FC = () => {
  const [options, setOptions] = useState<SelectProps["options"]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedStore, setSelectedStore] = useState<StoreDataProps | null>(null);
  const [menuName, setMenuName] = useState("");

  const fetchStores = async (search: string) => {
    if (!search) return;
    setLoading(true);
    try {
      const response = await axiosInstance.get(`api/stores/?search=${search}`);
      const data = response.data;
      const storeOptions = (data.objects || []).map((store: StoreDataProps) => ({
        value: store.id,
        label: `${store.name} (${store.code})`,
        third_party_id: store.third_party_id,
      }));
      setOptions(storeOptions);
    } catch (error) {
      console.error("Error fetching stores:", error);
    } finally {
      setLoading(false);
    }
  };

  const generateMenu = async () => {
    if (!menuName.trim()) {
      message.error("Please enter a menu name");
      return;
    }

    try {
      const response = await axiosInstance.post(
        `api/menu/generate-store-menu/${selectedStore?.third_party_id}/`,
        {
          name: menuName,
          description: "This is a sample testing menu",
        }
      );
      if (response.status === 200) {
        message.success(response.data.message);
      } else {
        message.error("Something went wrong");
      }
    } catch (error) {
      message.error("Something went wrong");
    }
  };

  const handleSelect = (value: number) => {
    const store = options?.find((option) => option.value === value);
    if (store) {
      setSelectedStore({
        id: value,
        name: store.label as string,
        code: "",
        third_party_id: store.third_party_id || "",
      });
    }
  };

  useEffect(() => {
    console.log(selectedStore);
  }, [selectedStore]);

  return (
    <div>
      <Select
        showSearch
        placeholder="Search and select store"
        notFoundContent={loading ? <Spin size="small" /> : null}
        filterOption={false}
        onSearch={fetchStores}
        onChange={handleSelect}
        style={{ width: "100%", marginBottom: "10px" }}
        options={options || []}
      />
      <Input
        placeholder="Enter menu name"
        value={menuName}
        onChange={(e) => setMenuName(e.target.value)}
        style={{ width: "100%", marginBottom: "10px" }}
      />
      {selectedStore && (
        <button
          className="bg-[#FF8732] text-white px-4 py-2 mt-4 rounded-lg"
          onClick={generateMenu}
        >
          Generate Menu
        </button>
      )}
    </div>
  );
};

export default MenuGenerate;
