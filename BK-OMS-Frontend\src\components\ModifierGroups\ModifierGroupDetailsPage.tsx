import React, { useCallback, useEffect, useState } from "react";
import { Card, Spin } from "antd";
import { EditOutlined } from "@ant-design/icons";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
import "../Products/Products.css";
import { ModifierGroupDetailsPageType } from "../../types/Products";
import ModifierGroupDetails from "./ModifierGroupDetails";
import BackButton from "../UI/BackButton";

const tabList = [
  { key: "Details", tab: "Details" },
  { key: "Modifiers", tab: "Modifiers" },
];

const ModifierGroupDetailsPage: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [storeDetails, setStoreDetails] =
    useState<ModifierGroupDetailsPageType | null>(null);
  const [loading, setLoading] = useState(false);

  const [searchParams, setSearchParams] = useSearchParams();
  const queryParams = new URLSearchParams(location.search);
  const modifierName = queryParams.get("modifiergroup_name") || "";
  const activeTabKey1 = searchParams.get("tab") || "Details";

  const fetchStore = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(
        `api/menu/modifier-groups/${id}/`
      );
      if (response.status === 200) {
        console.log(response.data);
        setStoreDetails(response.data);
      } else {
        setStoreDetails(null);
      }
    } catch (error) {
      console.error("Error fetching menu data", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStore();
  }, [id]);

  const onTabChange = useCallback(
    (key: string) => {
      setSearchParams(
        (prev) => {
          const params = new URLSearchParams(prev.toString());
          if (key === "Details") {
            params.set(modifierName, modifierName);
            params.delete("tab");
          } else {
            params.set("tab", key);
          }
          return params;
        },
        { replace: true }
      );
    },
    [modifierName, setSearchParams]
  );

  const contentList: Record<string, React.ReactNode> = {
    Details: storeDetails ? (
      <div className="flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Modifier Group Details</h2>
          <div>
            <button
              className="text-white px-4 py-2 rounded transition"
              style={{ backgroundColor: "#FF8732" }}
              onMouseOver={(e) =>
                (e.currentTarget.style.backgroundColor = "#e86f1a")
              }
              onMouseOut={(e) =>
                (e.currentTarget.style.backgroundColor = "#FF8732")
              }
              onClick={() => navigate(`/modifiers-groups-edit/${id}`)}
            >
              <EditOutlined /> Edit
            </button>
          </div>
        </div>

        <div className="space-y-2">
          <p>
            <strong>ID:</strong> {storeDetails.id}
          </p>
          <p>
            <strong>Code:</strong> {storeDetails.code}
          </p>
          <p>
            <strong>Name:</strong> {storeDetails.name}
          </p>
          <p>
            <strong>Display Name:</strong> {storeDetails.display_name}
          </p>
          <p>
            <strong>Description:</strong> {storeDetails.description}
          </p>
          <p>
            <strong>Position:</strong> {storeDetails.position}
          </p>
          <p>
            <strong>Category:</strong> {storeDetails.category}
          </p>
          <p>
            <strong>Section:</strong> {storeDetails.section}
          </p>
          <p>
            <strong>Max Seletable:</strong> {storeDetails.max_selectable}
          </p>
          <p>
            <strong>Status:</strong>
            {storeDetails.is_active ? "Active" : "Inactive"}
          </p>
        </div>
      </div>
    ) : (
      <p>Loading...</p>
    ),
    Modifiers: <ModifierGroupDetails />,
    // ModifierGroupEditPage: <ModifierGroupEditPage />,

    // Child_Variants: <ChildVariants />,
  };

  return (
    <>
      {loading ? (
        <div className="d-flex justify-content-center align-items-center">
          <Spin size="large" />
        </div>
      ) : (
        <>
          <div>
            <BackButton to={`/modifiers-groups`} />
          </div>
          <Card
            className="w-full"
            title={<h3>{modifierName || storeDetails?.name}</h3>}
            tabList={tabList}
            activeTabKey={activeTabKey1}
            onTabChange={onTabChange}
          >
            {contentList[activeTabKey1]}
          </Card>
        </>
      )}

      <br />
      <br />
    </>
  );
};

export default ModifierGroupDetailsPage;
