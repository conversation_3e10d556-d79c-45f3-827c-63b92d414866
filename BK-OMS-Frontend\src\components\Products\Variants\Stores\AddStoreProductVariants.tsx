import React, { useEffect, useState } from "react";
import { Form, Input, Button, message, Popover } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import "../../../Stores/addStore/AddStore.css";
import { axiosInstance } from "../../../../apiCalls";
import { StoreDataProps, StoreCatagoryProps } from "../../../../types";

interface AddStoreProductVariantProps {
  store: number;
  variant: number;
  category: number;
  position: number;
}

const AddStoreProductVariants: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { id, variantId } = useParams() as { id: string; variantId: string };
  // const [loading, setLoading] = useState<boolean>(false);

  // Store Search
  const [storeSearch, setStoreSearch] = useState("");
  const [storeResults, setStoreResults] = useState<StoreDataProps[]>([]);
  const [storeId, setStoreId] = useState<number | null>(null);
  const [storePopoverVisible, setStorePopoverVisible] = useState(false);

  // Variant (Taken from Params)
  // const [variantId] = useState<number>(Number(variantid));

  // Category Search
  const [categorySearch, setCategorySearch] = useState("");
  const [categoryResults, setCategoryResults] = useState<StoreCatagoryProps[]>(
    []
  );
  const [categoryId, setCategoryId] = useState<number | null>(null);
  const [categoryPopoverVisible, setCategoryPopoverVisible] = useState(false);

  const [storeData, setStoreData] = useState<AddStoreProductVariantProps>({
    store: 0,
    //@ts-ignore
    variant: variantId,
    category: 0,
    position: 0,
  });

  const onValuesChange = (
    _: Partial<AddStoreProductVariantProps>,
    allValues: AddStoreProductVariantProps
  ) => {
    setStoreData({
      ...allValues,
      store: storeId || 0,
      //@ts-ignore
      variant: variantId,
      category: categoryId || 0,
    });
  };

  /** Fetch Stores from API */
  const fetchStores = async () => {
    // setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/stores/?search=${storeSearch}`
      );
      if (response.status === 200) {
        setStoreResults(response.data.objects);
      }
    } catch (error) {
      console.error("Error fetching stores", error);
    } finally {
      // setLoading(false);
    }
  };

  /** Fetch Categories from API */
  const fetchCategories = async () => {
    // setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/categories/?search=${categorySearch}`
      );
      if (response.status === 200) {
        setCategoryResults(response.data.objects);
      }
    } catch (error) {
      console.error("Error fetching categories", error);
    } finally {
      // setLoading(false);
    }
  };

  useEffect(() => {
    if (storeSearch.length >= 3) fetchStores();
  }, [storeSearch]);

  useEffect(() => {
    if (categorySearch.length >= 3) fetchCategories();
  }, [categorySearch]);

  /** Form Submission */
  const onFinish = async () => {
    // console.log(variantId,variantid);
    try {
      const response = await axiosInstance.post(
        `api/menu/v2/store-product-variants/${variantId}/`,
        storeData
      );
      if (response.status === 201) {
        message.success("Store Product Variant Added Successfully!");
        navigate(`/stores/${id}/${storeId}/variants`);
      }
    } catch (error: any) {
      message.error(
        `Failed to add variant: ${
          error.response?.data?.message || "Unknown error"
        }`
      );
    }
  };

  /** Popover Content for Stores */
  const storePopoverContent = (
    <div>
      {storeResults.map((store) => (
        <div
          key={store.id}
          onClick={() => {
            setStoreSearch(store.name);
            setStoreId(store.id);
            setStorePopoverVisible(false);
          }}
          style={{ cursor: "pointer", padding: "5px" }}
        >
          {store.name}
        </div>
      ))}
    </div>
  );

  /** Popover Content for Categories */
  const categoryPopoverContent = (
    <div>
      {categoryResults.map((category) => (
        <div
          key={category.id}
          onClick={() => {
            setCategorySearch(category.name);
            setCategoryId(category.id);
            setCategoryPopoverVisible(false);
          }}
          style={{ cursor: "pointer", padding: "5px" }}
        >
          {category.name}
        </div>
      ))}
    </div>
  );

  return (
    <div>
      <h3>Add Store Product Variant</h3>
      <Form
        form={form}
        name="add_store_variant"
        layout="vertical"
        onFinish={onFinish}
        onValuesChange={onValuesChange}
        initialValues={storeData}
        className="add-store-form"
      >
        {/* Store ID with Popover Search */}
        <Form.Item
          name="store"
          label="Store"
          rules={[{ required: true, message: "Please select a store" }]}
          className="form-item"
        >
          <Popover
            content={storePopoverContent}
            title="Search Results"
            trigger="click"
            placement="bottom"
            open={
              storeSearch.length >= 3 &&
              storeResults.length > 0 &&
              storePopoverVisible
            }
            onOpenChange={(open) => setStorePopoverVisible(open)}
          >
            <Input
              placeholder="Search Store"
              value={storeSearch}
              onChange={(e) => {
                setStoreSearch(e.target.value);
                setStorePopoverVisible(true);
              }}
            />
          </Popover>
        </Form.Item>

        {/* Category ID with Popover Search */}
        <Form.Item
          name="category"
          label="Category"
          rules={[{ required: true, message: "Please select a category" }]}
          className="form-item"
        >
          <Popover
            content={categoryPopoverContent}
            title="Search Results"
            trigger="click"
            placement="bottom"
            open={
              categorySearch.length >= 3 &&
              categoryResults.length > 0 &&
              categoryPopoverVisible
            }
            onOpenChange={(open) => setCategoryPopoverVisible(open)}
          >
            <Input
              placeholder="Search Category"
              value={categorySearch}
              onChange={(e) => {
                setCategorySearch(e.target.value);
                setCategoryPopoverVisible(true);
              }}
            />
          </Popover>
        </Form.Item>

        {/* Position (Simple Input) */}
        <Form.Item
          name="position"
          label="Position"
          rules={[{ required: true, message: "Please enter position" }]}
          className="form-item"
        >
          <Input type="number" placeholder="Enter position" />
        </Form.Item>

        <Form.Item className="form-item">
          <Button type="primary" htmlType="submit" className="submit-button">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddStoreProductVariants;
