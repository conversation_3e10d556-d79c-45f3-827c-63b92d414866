import { useState, useEffect, useCallback } from "react";
import {
  Form,
  Input,
  Button,
  message,
  Popover,
  InputNumber,
  Radio,
  Select,
  Alert,
} from "antd";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
import { debounce } from "lodash";
import useTags from "../../customHooks/usetags";
import BackButton from "../UI/BackButton";

interface AddProductVariantProps {
  base_product: number;
  code: string;
  sku: string;
  name: string;
  display_name: string;
  description: string;
  product_type: string;
  is_crown_product?: boolean;
  combined_sections: boolean;
  loyalty_offer_code?: string;
  crown_points: string;
  base_product_name: string;
  default_pop_variant: number;
  default_pop_variant_name: string;
  discounted_price: number;
  price: number;
  tags: string[];
  unavailability_tags: string[];
}
interface Tags {
  id: number;
  name: string;
}
export const UNAVAILABILITY_TAGS_CHOICES = [
  { label: "ala_carte", value: "ala_carte" },
  { label: "meal", value: "meal" },
  { label: "combo", value: "combo" },
  { label: "modifier", value: "modifier" },
];

const MasterLevelVariantEdit = () => {
  const { id } = useParams();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const name = queryParams.get("name");

  const [form] = Form.useForm();
  const navigate = useNavigate();
  const isCrownProduct = Form.useWatch("is_crown_product", form);

  // const [productData, setProductData] = useState<AddProductVariantProps | null>(
  //   null
  // );
  const [baseProductSearch, setBaseProductSearch] = useState("");
  const [baseProductResults, setBaseProductResults] = useState<any[]>([]);
  const [baseProductPopoverVisible, setBaseProductPopoverVisible] =
    useState(false);
  const [selectedBaseProductId, setSelectedBaseProductId] = useState<number>(0);
  // Add these inside your component
  const [defaultPopVariantSearch, setDefaultPopVariantSearch] = useState("");
  const [defaultPopVariantResults, setDefaultPopVariantResults] = useState<
    any[]
  >([]);
  const [forPopOverSearch, setForPopOverSearch] = useState("");
  const [defaultPopVariantPopoverVisible, setDefaultPopVariantPopoverVisible] =
    useState(false);
  const [selectedDefaultPopVariantId, setSelectedDefaultPopVariantId] =
    useState<number>(0);

  const { tags, isLoading, error } = useTags();
  // const [tags, setTags] = useState<Tags[]>([]);

  useEffect(() => {
    const fetchProductVariant = async () => {
      try {
        const response = await axiosInstance.get(
          `api/menu/v2/product-variants/${id}/${id}/`
        );
        if (response.status === 200) {
          const data = response.data;
          //setProductData(data);
          setBaseProductSearch(data.base_product_name);
          setSelectedBaseProductId(data.base_product);
          setDefaultPopVariantSearch(data.default_pop_variant_name || "");
          setSelectedDefaultPopVariantId(data.default_pop_variant);

          form.setFieldsValue(data);
        }
      } catch (error) {
        message.error("Failed to load product variant details");
      }
    };

    fetchProductVariant();
  }, [id, form]);

  // Base product search
  const fetchBaseProducts = useCallback(
    debounce(async (searchText: string) => {
      if (searchText.length >= 3) {
        try {
          const response = await axiosInstance.get(
            `api/menu/products/?search=${searchText}`
          );
          if (response.status === 200) {
            setBaseProductResults(response.data.objects);
            setBaseProductPopoverVisible(response.data.objects.length > 0);
          }
        } catch (error) {
          console.error("Error fetching base products:", error);
        }
      } else {
        setBaseProductResults([]);
        setBaseProductPopoverVisible(false);
      }
    }, 500),
    []
  );
  // Debounced fetch for Default Pop Variant
  const fetchDefaultPopVariants = useCallback(
    debounce(async (searchText: string) => {
      if (searchText.length >= 3) {
        try {
          const response = await axiosInstance.get(
            `api/menu/v2/product-variants-list/?search=${searchText}`
          );
          if (response.status === 200) {
            setDefaultPopVariantResults(response.data.objects);
            setDefaultPopVariantPopoverVisible(
              response.data.objects.length > 0
            );
          }
        } catch (error) {
          console.error("Error fetching default pop variants:", error);
        }
      } else {
        setDefaultPopVariantResults([]);
        setDefaultPopVariantPopoverVisible(false);
      }
    }, 500),
    []
  );
  useEffect(() => {
    if (defaultPopVariantSearch.length >= 3) {
      fetchDefaultPopVariants(defaultPopVariantSearch);
    }
  }, [defaultPopVariantSearch, fetchDefaultPopVariants]);

  useEffect(() => {
    if (baseProductSearch.length >= 3) {
      fetchBaseProducts(baseProductSearch);
    }
  }, [baseProductSearch, fetchBaseProducts]);

  // const onValuesChange = (
  //   _: Partial<AddProductVariantProps>,
  //   allValues: AddProductVariantProps
  // ) => {
  //   setProductData({ ...allValues, base_product: selectedBaseProductId });
  // };

  const onFinish = async (values: AddProductVariantProps) => {
    try {
      const payload = {
        base_product_name: values.base_product_name,
        code: values.code,
        sku: values.sku,
        name: values.name,
        display_name: values.display_name,
        description: values.description,
        product_type: values.product_type,
        is_crown_product: values.is_crown_product,
        loyalty_offer_code: values.loyalty_offer_code,
        crown_points: values.crown_points,
        base_product: selectedBaseProductId,
        default_pop_variant_name: values.default_pop_variant_name,
        default_pop_variant: selectedDefaultPopVariantId,
        combined_sections: values.combined_sections,
        price: values.price,
        tags: values.tags,
        unavailability_tags: values.unavailability_tags,
      };
      const response = await axiosInstance.patch(
        `api/menu/v2/product-variants/${id}/${id}/`,
        {
          ...payload,
        }
      );
      if (response.status === 200 || response.status === 201) {
        message.success("Product Variant updated successfully!");
        navigate(`/variants/${id}`);
      }
    } catch (error: any) {
      message.error(
        `Failed to update product: ${
          error.response?.data?.message || "Unknown error"
        }`
      );
    }
  };

  if (!isLoading && error) {
    return (
      <div>
        <Alert message="Failed to load tags" description={error} type="error" />
        <BackButton />
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-lg font-semibold mb-4">Edit Variant: {name}</h2>
      <Form
        form={form}
        layout="vertical"
        // onValuesChange={onValuesChange}
        onFinish={onFinish}
        className="add-store-form"
      >
        <Form.Item label="Search Base Product">
          <Popover
            content={
              <ul className="max-h-48 overflow-y-auto">
                {baseProductResults.map((item) => (
                  <li
                    key={item.id}
                    onClick={() => {
                      setSelectedBaseProductId(item.id);
                      setBaseProductSearch(item.name); // Set search value to the selected product's name
                      setBaseProductPopoverVisible(false); // Close popover after selection
                    }}
                    className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                  >
                    {item.name}
                  </li>
                ))}
              </ul>
            }
            trigger="click"
            open={baseProductPopoverVisible && baseProductSearch.length >= 3}
          >
            <Input
              placeholder="Type to search..."
              value={baseProductSearch} // Controlled input with value set to state
              onChange={(e) => setBaseProductSearch(e.target.value)} // Update state with input value
            />
          </Popover>
        </Form.Item>
        <Form.Item label="Default Pop Variant Name">
          <Popover
            content={
              <ul className="max-h-48 overflow-y-auto">
                {defaultPopVariantResults.map((item) => (
                  <li
                    key={item.id}
                    onClick={() => {
                      setSelectedDefaultPopVariantId(item.id);
                      setDefaultPopVariantSearch(item.name); // Set the search value to the selected variant's name
                      setDefaultPopVariantPopoverVisible(false); // Close popover after selection
                    }}
                    className="px-3 py-2 hover:bg-gray-100 cursor-pointer"
                  >
                    {item.name}
                  </li>
                ))}
              </ul>
            }
            trigger="click"
            open={
              forPopOverSearch.length >= 3 && defaultPopVariantPopoverVisible
            }
          >
            <Input
              placeholder="Type to search Default Pop Variant..."
              value={defaultPopVariantSearch || ""} // Use the state directly here
              onChange={(e) => {
                const value = e.target.value;
                setDefaultPopVariantSearch(value);
                setForPopOverSearch(e.target.value);
                if (value.length >= 3) {
                  setDefaultPopVariantPopoverVisible(true); // Open popover if input length is >= 3
                } else {
                  setDefaultPopVariantPopoverVisible(false); // Close popover if input length is less than 3
                }
              }} // Update the state when the user types
            />
          </Popover>
        </Form.Item>

        {/* 
        <Form.Item name="code" label="Code" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item name="sku" label="SKU" rules={[{ required: true }]}>
          <Input /> */}
        {/* </Form.Item> */}
        <Form.Item name="name" label="Name" rules={[{ required: true }]}>
          <Input />
        </Form.Item>
        <Form.Item name="display_name" label="Display Name">
          <Input />
        </Form.Item>
        <Form.Item name="description" label="Description">
          <Input.TextArea />
        </Form.Item>

        <Form.Item
          name="product_type"
          label="Product Variant Type"
          rules={[{ required: true, message: "Please select a product type" }]}
        >
          <Select placeholder="Select a product type">
            <Select.Option value="ala_carte">Ala Carte</Select.Option>
            <Select.Option value="meal">Meal</Select.Option>
            <Select.Option value="combo">Combo</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item name="tags" label="Tags">
          <Select mode="multiple" placeholder="Select product types" allowClear>
            {tags.map((type: Tags) => (
              <Select.Option key={type.id} value={type.id}>
                {type.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item name="unavailability_tags" label="Unavailability Tags">
          <Select
            mode="multiple"
            placeholder="Select unavailability tags"
            allowClear
          >
            {UNAVAILABILITY_TAGS_CHOICES.map((type) => (
              <Select.Option key={type.label} value={type.value}>
                {type.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          name="combined_sections"
          label="Combined Sections"
          initialValue={false}
          rules={[{ required: true, message: "Please select an option" }]}
        >
          <Radio.Group>
            <Radio value={true}>True</Radio>
            <Radio value={false}>False</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          name="is_crown_product"
          label="Is Crown Product"
          initialValue={false}
          rules={[{ required: true, message: "Please select an option" }]}
        >
          <Radio.Group>
            <Radio value={true}>True</Radio>
            <Radio value={false}>False</Radio>
          </Radio.Group>
        </Form.Item>

        {isCrownProduct === true && (
          <>
            <Form.Item
              name="loyalty_offer_code"
              label="Loyalty Offer Code"
              rules={[{ required: true, message: "Please enter offer code" }]}
            >
              <Input placeholder="Enter loyalty offer code" />
            </Form.Item>

            <Form.Item
              name="crown_points"
              label="Crown Points"
              rules={[{ required: true, message: "Please enter crown points" }]}
            >
              <InputNumber
                style={{ width: "100%" }}
                placeholder="Enter crown points"
              />
            </Form.Item>

            <Form.Item
              name="price"
              label="Price"
              rules={[{ required: true, message: "Please enter Price" }]}
            >
              <InputNumber
                style={{ width: "100%" }}
                placeholder="Enter price"
              />
            </Form.Item>
          </>
        )}

        <Form.Item>
          <Button className="btn-save" type="primary" htmlType="submit">
            Update Variant
          </Button>
          <Button
            className="btn-cancel"
            onClick={() => navigate(-1)}
            type="default"
          >
            Cancel
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default MasterLevelVariantEdit;
