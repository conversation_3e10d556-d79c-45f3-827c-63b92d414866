import React, { useCallback, useState } from "react";
import { Form, Input, Button, message, Switch } from "antd";
import { useNavigate } from "react-router-dom";
import { axiosInstance } from "../../../../apiCalls";
import { StoreGroup } from "../StoreGroupsList/StoreGroupsList";
import BackButton from "../../../UI/BackButton";

const AddStoreGroup: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const handleSubmit = useCallback(
    async (values: StoreGroup) => {
      setLoading(true);
      try {
        const response = await axiosInstance.post(
          "/api/stores/groups/",
          values
        );

        if (response.status === 201 || response.status === 200) {
          message.success("Store group created successfully!");
          navigate("/store-groups");
        }
      } catch (error: any) {
        console.error("Error creating store group:", error);
        const errorMessage =
          error.response?.data?.message || "Failed to create store group.";
        message.error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    [navigate]
  );

  return (
    <div>
      <BackButton to={`/store-groups`} />
      <div className="p-4">
        <h2 className="text-xl font-semibold mb-4"> Create New Store Group</h2>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            name: "",
            description: "",
            is_active: true,
          }}
          className="add-store-form"
        >
          <Form.Item
            label="Name"
            name="name"
            rules={[{ required: true, message: "Please enter the store name" }]}
          >
            <Input placeholder="Enter Store Group Name" />
          </Form.Item>

          <Form.Item
            label="Description"
            name="description"
            rules={[
              { required: true, message: "Please enter the description" },
              { max: 255, message: "Description cannot exceed 255 characters" },
            ]}
          >
            <Input.TextArea rows={3} placeholder="Enter Description" />
          </Form.Item>

          <Form.Item
            label="Active Status"
            name="is_active"
            valuePropName="checked"
          >
            <Switch defaultChecked />
          </Form.Item>

          <Form.Item>
            <Button
              className="btn-save"
              type="primary"
              htmlType="submit"
              loading={loading}
              block
            >
              Create Store Group
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default AddStoreGroup;
