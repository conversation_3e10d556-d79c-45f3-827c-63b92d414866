import { ProductVariantModiferProps } from "../../../../types/Products";
import { Typography } from "antd";
import { useParams } from "react-router-dom";
import StoreTabs from "../../../../reusableComponents/store/StoreTabs";
const { Link } = Typography;

const ListingModifiers = () => {
  // const navigate = useNavigate();
  const { id } = useParams() as { id: string };
  const {code} =useParams() as {code:string};
  const { variantid } = useParams() as { variantid: string };
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      fixed: "left" as "left",
      render: (text: string, _: ProductVariantModiferProps) => (
        <span onClick={() => console.log("hhhh")}>
          <Link>{text}</Link>
        </span>
      ),
    },
    {
      title: "Modifier",
      dataIndex: "modifier",
      key: "modifier",
    },

    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      render: (isActive: boolean) => (
        <span>{isActive ? "Active" : "In active"}</span>
      ),
    },
  ];


  const mapData = (response: any): ProductVariantModiferProps[] =>
    response.objects.map((item: any) => ({
      key: item.id,
      id: item.id,
      position: item.position,
      product_variant: item.product_variant,
      is_active: item.is_active,
      modifier:item.modifier,

    }));
  return (
    <StoreTabs
      id={variantid}
      apiEndpoint={`api/menu/v2/product-variant-modifiers`}
      name="Product Variant  MODIFIERS"
      columns={columns}
      dataMapper={mapData}
      // itemPath={itemPath}
      add={`../products/${id}/${code}/variants/modifier/${variantid}/add/`}
    />
  );
};

export default ListingModifiers;
