body {
    font-family: "Poppins", serif !important;
  }
  .order-details-container {
    padding: 24px;
    background: #fff;
    min-height: 100vh;
  }
  
  /* .order-details-card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      padding: 24px;
    } */
  
  /* .heading-orders {
    border-bottom: 1px solid #f1f1f1;
    height: 50px;
    border-radius: 8px 8px 0 0; 
    transition: background-color 0.3s ease, color 0.3s ease;
    padding: 4px 16px; 
    background-color: #fff; 
  } */
  
  .store-name-highlight {
    color: #ff8732;
    font-weight: bold;
  }
  .order-detail-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
  }
  .button-back {
    display: flex;
    justify-content: end;
    align-items: center;
    margin-top: 30px;
  }
  
  .button-back :hover {
    background-color: #ff8732 !important;
    color: #fff !important;
  }
  
  /* .button-back svg { */
  /* li.ant-menu-item span {
    color: white !important;
  }
  li.ant-menu-item span svg {
    color: white !important;
  }
  svg path {
    color: white !important;
  } */
  .ant-card.ant-card-bordered.css-dev-only-do-not-override-1kf000u {
    border: none;
    padding: 0px !important;
  }
  /* .ant-card-body {
    padding: 0px !important;
  } */
  
  /* Item Details  table*/
  /* General styles */
  .items-container {
    font-family: "Poppins", serif;
  }
  
  .items-title {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
    color: #ff9900;
  }
  
  /* Table styles */
  .items-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 30px;
    font-size: 14px;
    margin-right: 20px; /* Added margin to create space on the right */
  }
  
  .items-table-header th {
    padding: 10px;
    font-weight: bold;
    background-color: #f8f9fa;
    text-align: left;
  }
  
  .items-table-row td {
    padding: 10px;
    border-bottom: 1px solid #ddd;
    text-align: left;
  }
  
  /* Totals section styles */
  .totals-container {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
    margin-right: 20px; /* Increased space on the right side */
  }
  
  .totals-table {
    width: 40%;
    border-collapse: collapse;
    text-align: center;
    font-size: 14px;
    margin-right: 20px; /* Added margin to create space on the right */
  }
  
  .totals-label {
    padding: 10px;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
  }
  
  .totals-value {
    padding: 10px;
    border-bottom: 1px solid #ddd;
  }
  
  .totals-grand-label {
    padding: 10px;
    font-weight: bold;
  }
  
  .totals-grand-value {
    padding: 10px;
    font-weight: bold;
    font-size: 18px;
    border-top: 2px solid #000;
  }
  .table th {
    color: #999999;
    min-width: 50px;
  }
  th:first-child,
  td:first-child {
    padding-left: 0;
  }
  
.order-details-value {
    display: flex
;
}
.order-details-label {
    width: 25%;
}

/* orders filters & search & date range */

.search-btn-driver {
  /* position: relative; */
  flex-grow: 1; /* Makes it flexible */
 
}


.search-btn-driver input {
  padding: 10px;
  width: 100%;
  border: 1px solid #d9d9d9;
  border-radius: 50px;
  font-family: "poppins", sans-serif !important;
}



.search-btn-driver svg {
  fill: #ff8732; 
}
.search-input svg {
  fill: #ff8732; 
}


.date-btn-driver {
  flex-grow: 1; /* Makes it responsive */
  min-width: 250px;
  max-width: 400px;
}
.date-btn-driver .ant-picker {
  padding: 10px;
  width: 75%;
  border: 1px solid #d9d9d9;
  border-radius: 50px;
}
/* .search-btn-driver button.ant-btn {
  border-radius: 60px;
  border-left: 2px !important;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
} */

/* search icone */
/* .search-container {
  width: 35%;
  position: relative;
} */

/* .search-container .ant-input {
  padding-left: 15px;
  padding-right: 15px;
  font-size: 14px;
  height: 45px;
  border-radius: 50px;
  border: 1px solid #d9d9d9;
  box-sizing: border-box;
  transition: all 0.3s ease-in-out;
} */

.search-container .ant-input:focus {
  border-color: #ff0000;
  box-shadow: 0 0 5px rgba(255, 0, 0, 0.5);
  /* padding-left: 40px !important;  */
}

.search-container .search-icon {
  font-size: 18px;
  color: #ff0000; 
}

/* filter btn */
/* .filter-btn {
  margin-left: 10px;
  border: 1px solid #d9d9d9;
  background: #f5f5f5;
} */
/* wrap clear */
/* .d-flex.flex-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-width: 100%;
} */

/* filter input */

.filter-dropdown {
  min-width: 150px; 
  max-width: 200px;
}

.filter-dropdown .ant-input {
  width: 100%;
}
.filter-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  transition: background-color 0.3s, color 0.3s;
}

.filter-icon.active {
  background-color: #1677ff;
  color: #fff;
}

.filter-icon.inactive {
  background-color: transparent;
  color: #1890ff;
}

.search-filter-clear-btn {
  cursor: pointer;
  margin-left: 10px;
  font-size: 14px;
}


/* Items Table styles */
.items-table {
  width: 100%;
  border-collapse: collapse;
  overflow-x: auto;
  margin-bottom: 0px;
  font-size: 14px;
  margin-right: 20px;
  border: none !important;
}

.items-table-header th {
  padding: 10px;
  font-weight: bold;
  background-color: #f8f9fa;
  text-align: right;
}

.items-table-row td {
  padding: 10px;
  border-bottom: 1px solid #ddd;
  text-align: right;
}

/* Column-Specific Styling */
.items-table th:first-child,
.items-table td:first-child,
.items-table th:nth-child(2),
.items-table td:nth-child(2),
.items-table th:nth-child(3),
.items-table td:nth-child(3) {
  padding-left: 4px;
  text-align: left;
}

.items-table th:first-child,
.items-table td:first-child {
  width: 500px;
}

/* Totals  table section styles */
.totals-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
  border: none !important;
  margin-left: 300px;
  margin-right: 19px;
}

.totals-table {
  width: 40%;
  border-collapse: collapse;
  overflow-x: auto;
  text-align: center;
  font-size: 14px;
  margin-right: -26px;
  border: none !important;
}

.totals-label {
  padding: 10px;
  font-weight: bold;
  border-bottom: 1px solid #ddd;
}
.totals-label:first-child {
  text-align: left;
}

.totals-value {
  padding: 10px;
  border-bottom: 1px solid #ddd;
  text-align: right;
  padding-right: 29px;
}

.totals-grand-label {
  padding: 10px;
  font-weight: bold;
}
.totals-grand-label:first-child {
  text-align: left;
}

.totals-grand-value {
  padding: 10px;
  font-weight: bold;
  font-size: 18px;
  /* border-top: 2px solid #000; */
  padding-left: 30px;
}
.table th {
  color: #999999;
  min-width: 50px;
}


/* Store filter styles */
.filter-dropdown {
  padding: 8px;
}

.filter-input-wrapper {
  position: relative;
}

/* .filter-input {
  width: 100%;
} */

.clear-icon {
  cursor: pointer;
  color: #999;
  opacity: 1;
  transition: opacity 0.2s ease-in-out;
}

.clear-icon.hidden {
  opacity: 0;
  pointer-events: none;
}
.filter-options {
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
}
/* .filter-option {
  padding: 4px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
} */

.filter-option:hover {
  background-color: #f5f5f5;
}

.no-suggestions {
  padding: 4px;
  color: #999;
}

.filter-option {
  padding: 8px;
  cursor: pointer;
}

.filter-option.highlighted {
  background-color: #f0f0f0;
}
/* filter btn */
.filter-btn {
  margin-left: 10px;
  border: 1px solid #d9d9d9;
  background: #f5f5f5;
}
/* wrap clear */
.d-flex.flex-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-width: 100%;
}

/* badges for status in orderlist */
.status-wrapper {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  width: 110px;
  text-align: center;
  padding: 2px;
}

/* Status Tag */
.status-tag {
  font-weight: 300;
  font-size: 12px;

  border-radius: 12px;
  /* padding: 4px 12px; */
  background: transparent;
  color: white;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  border: none;
}

/* Status Colors */
.status-wrapper.completed {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: #fff;
}

.status-wrapper.new {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: #fff;
}

.status-wrapper.processing {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
  color: #fff;
}

.status-wrapper.default {
  background: gray;
  color: white;
}
.status-wrapper.paid {
  background: linear-gradient(135deg, #52c41a, #73d13d);
  color: #fff;
}

.status-wrapper.unpaid {
  background: linear-gradient(135deg, #f5222d, #ff4d4f);
  color: #fff;
}

.status-wrapper.expired {
  background: linear-gradient(135deg, #d4b106, #fadb14);
  color: #fff;
}

.status-wrapper.refunded {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: #fff;
}

.status-wrapper.refund-failed { /* Corrected from "Redund Failed" */
  background: linear-gradient(135deg, #ff5722, #ff7043);
  color: #fff;
}


/* Modal JSON Viewer styles */
.modal-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid #fa8431;
  padding-bottom: 10px;
  width: 100%;
}


