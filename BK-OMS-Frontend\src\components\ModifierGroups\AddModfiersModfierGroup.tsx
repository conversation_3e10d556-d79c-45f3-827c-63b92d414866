import  { useEffect, useState } from "react";
import { Form, Input, Button, message, Select, Popover, InputNumber } from "antd";
import { useNavigate, useParams } from "react-router-dom";
import { ProductDataProps } from "../../types";
import { axiosInstance } from "../../apiCalls";
import '../Stores/addStore/AddStore.css';
interface AddModifiersModifierGroupProps {
  modifier_group: number;
  modifier: number;
  price: number;
  position: number;
  default_qty: number;
  max_qty: number;
  upsell_to_packaged_version: boolean;
  is_active: boolean;
}

const AddModifiersModifierGroup = () => {
  const [form] = Form.useForm();
  const { id } = useParams();
  const queryParams = new URLSearchParams(location.search);
  const modifierName = queryParams.get("name");
  const navigate = useNavigate();
  const [storeSearch, setStoreSearch] = useState("");
  const [storeResults, setStoreResults] = useState<ProductDataProps[]>([]);
  const [storePopoverVisible, setStorePopoverVisible] = useState(false);

  const [modifierData, setModifierData] = useState<AddModifiersModifierGroupProps>({
    modifier_group: Number(id),
    modifier: 0,
    position: 0,
    price: 0,
    default_qty: 0,
    max_qty: 0,
    upsell_to_packaged_version: false,
    is_active: true,
  });

  useEffect(() => {
    form.setFieldsValue({ modifier_group: Number(id) });
  }, [id, form]);

  const onValuesChange = (_: Partial<AddModifiersModifierGroupProps>, allValues: AddModifiersModifierGroupProps) => {
    setModifierData({ ...allValues, modifier_group: Number(id) ?? 0 });
  };

  /** Fetch Stores from API */
  const fetchStores = async () => {
    try {
      const response = await axiosInstance.get(`api/menu/v2/modifiers/?search=${storeSearch}`);
      if (response.status === 200) {
        setStoreResults(response.data.objects);
      }
    } catch (error) {
      console.error("Error fetching stores", error);
    }
  };

  useEffect(() => {
    if (storeSearch.length >= 3) fetchStores();
  }, [storeSearch]);

  const onFinish = async () => {
    console.log("Submitting Data:", modifierData);
    try {
      const response = await axiosInstance.post(`api/menu/v2/modifier-variants/${id}/`, modifierData);
      if (response.status === 201) {
        message.success("Modifier Created Successfully!");
        navigate(`/modifiers-groups-details/${id}?modifiergroup_name=${modifierName}`);
      }
    } catch (error: any) {
      console.error("Error response from API:", error);
      message.error("Failed to create modifier.");
    }
  };

  const storePopoverContent = (
    <div>
      {storeResults.map((store) => (
        <div
          key={store.id}
          onClick={() => {
            setStoreSearch(store.name);
            setModifierData((prev) => ({
              ...prev,
              modifier: store.id,
            }));
            form.setFieldsValue({ modifier: store.id }); // ✅ Update form value explicitly
            setStorePopoverVisible(false);
          }}
          style={{ cursor: "pointer", padding: "5px" }}
        >
          {store.name}
        </div>
      ))}
    </div>
  );

  return (
    <div>
      <h3>Add Modifier</h3>
      <Form
        form={form}
        name="add_modifier"
        layout="vertical"
        onFinish={onFinish}
        onValuesChange={onValuesChange}
        initialValues={modifierData}
      >
        <Form.Item
          name="modifier"
          label="Modifier"
          rules={[{ required: true, message: "Please enter a modifier" }]}
        >
          <Popover
            content={storePopoverContent}
            title="Search Results"
            trigger="click"
            placement="bottom"
            open={storeSearch.length >= 3 && storeResults.length > 0 && storePopoverVisible}
            onOpenChange={(open) => setStorePopoverVisible(open)}
          >
            <Input
              placeholder="Search Modifier"
              value={storeSearch}
              onChange={(e) => {
                setStoreSearch(e.target.value);
                setStorePopoverVisible(true);
              }}
            />
          </Popover>
        </Form.Item>

        <Form.Item name="is_active" label="Status">
          <Select>
            <Select.Option value={true}>Active</Select.Option>
            <Select.Option value={false}>Inactive</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item name="position" label="Position">
          <InputNumber min={0} />
        </Form.Item>

        <Form.Item name="price" label="Price">
          <InputNumber min={0} />
        </Form.Item>

        <Form.Item name="default_qty" label="Default Quantity">
          <InputNumber min={0} />
        </Form.Item>

        <Form.Item name="max_qty" label="Max Quantity">
          <InputNumber min={0} />
        </Form.Item>

        <Form.Item name="upsell_to_packaged_version" label="Upsell to Packaged Version">
          <Select>
            <Select.Option value={true}>Yes</Select.Option>
            <Select.Option value={false}>No</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddModifiersModifierGroup;
