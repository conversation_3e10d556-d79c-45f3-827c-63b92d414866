import React, { useCallback, useEffect, useState } from "react";
import { Table, Button, notification, Pagination } from "antd";
import { Link, useNavigate, useParams } from "react-router-dom";
import type { TableRowSelection } from "antd/es/table/interface";
import { axiosInstance } from "../../../../apiCalls";

import axios from "axios";

export interface ActiveStoreConfig {
  id: number;
  name: string;
  code: string;
  is_selected: boolean;
}

export interface ActiveStoresResponse {
  objects: ActiveStoreConfig[];
  total_count: number;
}

const MapGroupToStores: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // State variables
  const [storeOptions, setStoreOptions] = useState<ActiveStoreConfig[]>([]);
  const [selectedStores, setSelectedStores] = useState<Set<number>>(new Set());
  const [initialSelectedStores, setInitialSelectedStores] = useState<
    Set<number>
  >(new Set());
  const [newlySelectedStores, setNewlySelectedStores] = useState<Set<number>>(
    new Set()
  );
  const [deselectedStores, setDeselectedStores] = useState<Set<number>>(
    new Set()
  );
  const [btnLoader, setBtnLoader] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [selectAllTitle, setSelectAllTitle] = useState<string>("Select All");

  // const [searchTerm, setSearchTerm] = useState<string>("");

  const updateURLParams = useCallback(
    (updatedFilters: Record<string, string | number | undefined>) => {
      const params = new URLSearchParams();
      Object.entries(updatedFilters).forEach(([key, value]) => {
        if (value !== undefined) params.set(key, String(value));
      });
      navigate(`?${params.toString()}`, { replace: true });
    },
    [navigate]
  );

  // Fetch stores on component mount or when page changes
  useEffect(() => {
    fetchStores(currentPage, pageSize);
  }, [currentPage, pageSize]);

  const fetchStores = async (page: number, size: number) => {
    setLoading(true);
    try {
      const response = await axiosInstance.get<ActiveStoresResponse>(
        `/api/stores/groups-stores-list/?group_id=${id}&page=${page}&page_size=${size}`
      );

      if (response.data?.objects) {
        const fetchedStores = response.data.objects.map(
          (store: ActiveStoreConfig) => ({
            id: store.id,
            name: store.name,
            code: store.code,
            is_selected: store.is_selected,
          })
        );

        setStoreOptions(fetchedStores);
        setTotalCount(response.data.total_count);

        //  Capture initially selected stores
        const initiallySelected = new Set(
          fetchedStores
            .filter((store) => store.is_selected)
            .map((store: ActiveStoreConfig) => store.id)
        );

        setInitialSelectedStores(initiallySelected);
        setSelectedStores(
          new Set([...initiallySelected, ...newlySelectedStores])
        );
      } else {
        setStoreOptions([]);
        // setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching stores:", error);
      notification.error({
        message: "Error",
        description: "Failed to load stores.",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const params = new URLSearchParams(location.search);

    setCurrentPage(parseInt(params.get("page") || "1", 10));
    setPageSize(parseInt(params.get("page_size") || "10", 10));
  }, [location.search]);

  const handlePageChange = (page: number, pageSize?: number) => {
    setCurrentPage(page);
    if (pageSize) setPageSize(pageSize);
    updateURLParams({ page, page_size: pageSize });
  };

  //  Handle row selection logic
  const rowSelection: TableRowSelection<ActiveStoreConfig> = {
    selectedRowKeys: Array.from(selectedStores),
    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedStores(new Set(selectedRowKeys.map(Number)));
      if (selectedRowKeys.length === storeOptions.length) {
        setSelectAllTitle("Deselect All");
      } else {
        setSelectAllTitle("Select All");
      }
    },
    onSelect: (record: ActiveStoreConfig, selected: boolean) => {
      setSelectedStores((prev) => {
        const newSet = new Set(prev);

        if (selected) {
          newSet.add(record.id);

          if (!initialSelectedStores.has(record.id)) {
            setNewlySelectedStores((prev) => new Set(prev).add(record.id));
          }

          setDeselectedStores((prev) => {
            const updatedDeselected = new Set(prev);
            updatedDeselected.delete(record.id);
            return updatedDeselected;
          });
        } else {
          newSet.delete(record.id);

          setNewlySelectedStores((prev) => {
            const updatedNewlySelected = new Set(prev);
            updatedNewlySelected.delete(record.id);
            return updatedNewlySelected;
          });

          if (initialSelectedStores.has(record.id)) {
            setDeselectedStores((prev) => new Set(prev).add(record.id));
          }
        }
        return newSet;
      });
    },
    onSelectAll: (
      selected: boolean,
      selectedRows: ActiveStoreConfig[],
      changeRows: ActiveStoreConfig[]
    ) => {
      void selectedRows;
      setSelectedStores((prevSelected) => {
        const updatedSelection = new Set(prevSelected);

        if (selected) {
          changeRows.forEach((store) => updatedSelection.add(store.id));

          setNewlySelectedStores((prev) => {
            const updatedNewlySelected = new Set(prev);
            changeRows.forEach((store) => {
              if (!initialSelectedStores.has(store.id)) {
                updatedNewlySelected.add(store.id);
              }
            });
            return updatedNewlySelected;
          });

          setDeselectedStores((prev) => {
            const updatedDeselected = new Set(prev);
            changeRows.forEach((store) => updatedDeselected.delete(store.id));
            return updatedDeselected;
          });
        } else {
          changeRows.forEach((store) => updatedSelection.delete(store.id));

          setNewlySelectedStores((prev) => {
            const updatedNewlySelected = new Set(prev);
            changeRows.forEach((store) =>
              updatedNewlySelected.delete(store.id)
            );
            return updatedNewlySelected;
          });

          setDeselectedStores((prev) => {
            const updatedDeselected = new Set(prev);
            changeRows.forEach((store) => {
              if (initialSelectedStores.has(store.id)) {
                updatedDeselected.add(store.id);
              }
            });
            return updatedDeselected;
          });
        }

        return updatedSelection;
      });
    },
    columnWidth: "5%",
  };

  const handleStoreMapping = async () => {
    if (!id) {
      notification.error({
        message: "Invalid Creative file",
        description: "No ID found for mapping.",
      });
      return;
    }

    // store_id: Includes newly selected stores (not part of initially selected stores)
    const finalStoreIds = Array.from(newlySelectedStores);

    // creative_removed_store_id: Includes initially selected stores that were later deselected
    const removedStoreIds = Array.from(deselectedStores);

    if (finalStoreIds.length === 0 && removedStoreIds.length === 0) {
      notification.warning({
        message: "No Changes Made",
        description:
          "Please select or deselect at least one store before saving.",
        placement: "topLeft",
      });
      return;
    }

    //Construct payload
    const payload = {
      group_id: Number(id),
      add_stores: finalStoreIds, // Only newly selected stores
      remove_stores: removedStoreIds, // Only initially selected & later deselected stores
    };

    try {
      setBtnLoader(true);
      const response = await axiosInstance.post(
        "/api/stores/groups-stores/",
        payload
      );
      if (response.status === 201 || response.status === 200) {
        notification.success({
          message: "Stores Updated",
          description: "Stores have been successfully updated.",
        });
        navigate(`/store-group-details/${id}?tab=Group_Mapped_Stores`);
      }
    } catch (error) {
      console.error("Error mapping stores:", error);

      let errorMessage = "Failed to update stores.";

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 400) {
          errorMessage = "Please Select or Deselect at least one store.";
        } else {
          errorMessage =
            error.response?.data?.message ||
            error.response?.data?.error ||
            JSON.stringify(error.response?.data) ||
            errorMessage;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      notification.error({
        message: "Error",
        description: errorMessage,
      });
    } finally {
      setBtnLoader(false);
    }
  };

  const columns = [
    {
      title: selectAllTitle,
      dataIndex: "",
      key: "",
      width: "5%",
      fixed: "left" as "left",
    },
    {
      title: "Store Name",
      dataIndex: "name",
      key: "name",
      width: "40%",
      render: (text: string, record: ActiveStoreConfig) =>
        text ? (
          <Link
            className="common-link text-decoration-none"
            to={`/stores/${record.id}/details`}
          >
            {text}
          </Link>
        ) : (
          "N/A"
        ),
    },
    {
      title: " Store Code",
      dataIndex: "code",
      key: "code",
      width: "30%",
    },
  ];

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div className="mt-2">
          <h2>Stores Mapping</h2>
        </div>
      </div>

      <>
        <div className="d-flex justify-content-end mt-3">
          <Button
            type="primary"
            className="btn-save"
            onClick={handleStoreMapping}
            disabled={btnLoader}
          >
            {btnLoader ? `Saving...` : `Save Selection`}
          </Button>
          <Button
            type="default"
            className="btn-cancel"
            onClick={() => navigate(-1)}
          >
            Cancel
          </Button>
        </div>
        <div className="mt-3">
          <Table
            rowSelection={rowSelection}
            dataSource={storeOptions}
            columns={columns}
            loading={loading}
            rowKey="id"
            pagination={false}
            scroll={{ x: "max-content" }}
          />
        </div>

        <div className="d-flex justify-content-end mt-3">
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            onChange={handlePageChange}
            showSizeChanger
          />
        </div>
      </>
    </div>
  );
};

export default MapGroupToStores;
