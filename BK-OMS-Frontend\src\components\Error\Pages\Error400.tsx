import { But<PERSON> } from "antd";
import { Container, Row, Col } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";
import Cookies from "js-cookie";

const Error400 = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const token = Cookies.get("token");
    const refreshToken = Cookies.get("refreshToken");
    if (!token || !refreshToken) {
      navigate("/");
    }
  }, [navigate]);

  const handleGoHome = () => {
    navigate("/products");
  };

  return (
    <Container
      fluid
      className="d-flex align-items-center justify-content-center vh-100 bg-light"
    >
      <Row className="justify-content-center p-5 w-100">
        <Col md={8} className="text-center bg-white rounded-4 shadow p-5">
          <div className="d-flex align-items-center justify-content-center mb-4">
            <svg
              width="120"
              height="120"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#fa541c"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mb-3"
            >
              <circle cx="12" cy="12" r="10" />
              <path d="M15 9l-6 6" />
              <path d="M9 9l6 6" />
            </svg>
          </div>
          <h1 className="fw-bold text-danger mb-3">400 - Bad Request</h1>
          <p className="text-secondary fs-5 mb-4">
            Oops! The server could not understand your request. Please try again
            or return to the homepage.
          </p>
          <Button
            type="primary"
            size="large"
            className="bg-danger text-white"
            shape="round"
            onClick={handleGoHome}
          >
            Go to Homepage
          </Button>
        </Col>
      </Row>
    </Container>
  );
};

export default Error400;
