import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { ChildVariantListProps, StoreMenuProps } from "../../types";
import {
  Table,
  Form,
  Popover,
  Button,
  Input,
  message,
  InputNumber,
} from "antd";

import "../Products/Products.css";
import { axiosInstance } from "../../apiCalls";

export interface ChildVariant {
  objects: ChildVariantListProps[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: number | null;
  previous_page: number | null;
  total_count: number;
}

const ChildVariantDetails: React.FC = () => {
  const [addNew, setAddNew] = useState(false);

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const name = queryParams.get("name")?.trim();
  const id = queryParams.get("store")?.trim(); // Access the 'storeid' query parameter
  const childVariant = queryParams.get("id")?.trim();
  // Access the 'id' query parameter
  // Access the 'name' query parameter
  const [searchInput, setSearchInput] = useState("");

  const [product, setProduct] = useState(0);

  const [loading, setLoading] = useState<boolean>(false);
  const [childVariants, setChildVariants] = useState<ChildVariant | null>(null);

  const [searchResults, setSearchResults] = useState<StoreMenuProps[]>([]);
  const [visible, setVisible] = useState(false);
  const [currentPage, setCurrentPage] = useState<number>(1);

  // const navigate = useNavigate();
  const [form] = Form.useForm();

  // Function to fetch modifiers
  const getSearchResult = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/store-modifier-groups/${id}/?search=${searchInput}`
      );
      if (response.status === 200) {
        setSearchResults(response.data.objects); // Set the results in the searchResults state
      } else {
        console.error("Error fetching modifiers", response.status);
      }
    } catch (error) {
      console.error("Error fetching modifiers", error);
    } finally {
      setLoading(false);
    }
  };
  const onSubmit = async (values: any) => {
    // Create payload object with product name and position

    try {
      const payload = {
        modifier_group: Number(product), // Convert storeProduct to a number
        position: Number(values.position),
        child_variant: Number(childVariant),
      };
      console.log(payload);
      const response = await axiosInstance.post(
        `api/menu/child-variants/${id}/${childVariant}/modifier-groups/`,
        payload
      );
      console.log(response);
      if (response.status === 201) {
        await message.success("Child Variants added Successfully!");
        window.location.reload();
      }
      if (response.status === 400) {
        
        message.error("failed to Child Variants");
      }
      
    } catch (error: any) {
      if (error.response) {
        console.error("Error response from API:", error.response.data);
        message.error(
          `failed to Child Variants: ${
            error.response.data.message || "Unknown error"
          }`
        );
      } else {
        console.error("failed to Child Variants:", error);
        message.error("failed to Child Variants.");
      }
    }
  };
  const popoverContent = (
    <div>
      <ul>
        {searchResults.map((result) => (
          <div
            key={result.id}
            onClick={() => {
              setSearchInput(result.variant_name);
              setProduct(result.id);

              setVisible(false); // Close popover after selection
            }}
            style={{ cursor: "pointer", padding: "5px" }}
          >
            {result.variant_name}
          </div>
        ))}
      </ul>
    </div>
  );
  const getChildVariants = async (page: number) => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/child-variants/${id}/${childVariant}/modifier-groups/`,
        {
          params: { page },
        }
      );
      if (response.status === 200) {
        setChildVariants(response.data);
        setLoading(false);
      } else {
        console.log("Error fetching menu data", response.status);
        setChildVariants(null);
        setLoading(false);
      }
    } catch (error) {
      console.error("Error fetching menu data", error);
      setLoading(false);
    }
  };
  useEffect(() => {
    if (searchInput.length >= 3) {
      getSearchResult(); // Trigger the API call only after 3 characters
    }
  }, [searchInput]);

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "20%",
      render: (text: string) => (
        <span>
          {/* // <span onClick={() => navigate(`/store/child-variant`)}> */}
          {text}
        </span>
      ),
    },

    {
      title: "Child Variant",
      dataIndex: "child_variant",
      key: "child_variant",
      width: "10%",
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
      width: "10%",
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      width: "10%",
      render: (isActive: boolean) => (
        <span>{isActive ? "Active" : "Inactive"}</span>
      ),
    },
  ];
  const data: ChildVariantListProps[] =
    childVariants?.objects?.map((ChildVariant) => ({
      key: ChildVariant.id,
      id: ChildVariant.id,
      position: ChildVariant.position,
      child_variant: ChildVariant.child_variant,
      is_active: ChildVariant.is_active,
      modifier_group: ChildVariant.modifier_group,
      name: ChildVariant.name || "",
    })) || [];
  useEffect(() => {
    if (id) {
      getChildVariants(currentPage);
    }
  }, [id, currentPage]);

  return (
    <div>
      <div className="main-dashboard-buttons">
        <button
          className="typography"
          onClick={() => {
            setAddNew(!addNew);
          }}
        >
          {" "}
          + Add New{" "}
        </button>
      </div>
      <div className="container product-card-banner">
        <div className="header products-headers">
          <div className="title">{name}</div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>

            {/* Search Input with Popover */}
          </div>
        </div>
        {addNew && (
          <Form
            form={form}
            name="horizontal_login"
            layout="inline"
            onFinish={onSubmit}
          >
            <Form.Item
              name="ModifierGroup"
              rules={[
                {
                  required: true,
                  message: "Please input your modifier varient!",
                },
              ]}
            >
              <Input
                placeholder="ModifierGroup"
                value={searchInput}
                onChange={(e) => {
                  setSearchInput(e.target.value);
                  setVisible(true); // Show popover when typing
                  form.setFieldsValue({ ModifierGroup: e.target.value });
                  // Update form state
                }}
              />
              <Popover
                content={popoverContent}
                title="Search Results"
                trigger="click"
                placement="bottom"
                open={
                  searchInput.length >= 3 && searchResults.length > 0 && visible
                }
                onOpenChange={(open) => setVisible(open)}
              ></Popover>
            </Form.Item>
            {/* 
            <Form.Item
              name="quantity"
              rules={[
                { required: true, message: "Please input Default Quantity" },
              ]}
            >
              <InputNumber
                min={0}
                max={100}
                className="input-field"
                type="quantity"
                placeholder="Quantity"
              />
            </Form.Item> */}
            <Form.Item
              name="position"
              rules={[
                { required: true, message: "Please input Default Quantity" },
              ]}
            >
              <InputNumber
                min={0}
                max={100}
                className="input-field"
                type="position"
                placeholder="Position"
              />
            </Form.Item>
            {/* <Form.Item
              name="price"
              rules={[{ required: true, message: "Please input Max Quantity" }]}
            >
              <InputNumber
                min={0}
                max={100}
                className="input-field"
                type="price"
                placeholder="Price"
              />
            </Form.Item> */}
            {/* <Form.Item
              name="section"
              rules={[{ required: true, message: "Please enter store code" }]}
            >
              <Select className="input-field" defaultValue="customize">
                <Select.Option value="customize" className="input-field">
                  Customize
                </Select.Option>
                <Select.Option value="choose_side" className="input-field">
                  Choose Side
                </Select.Option>
                <Select.Option value="choose_drink" className="input-field">
                  Choose Drink
                </Select.Option>
              </Select>
            </Form.Item> */}
            <Form.Item shouldUpdate>
              {() => (
                <Button className="typography" htmlType="submit">
                  Submit
                </Button>
              )}
            </Form.Item>
          </Form>
        )}
      </div>
      <div className="pt-4 mt-4">
        <Table<ChildVariantListProps>
          columns={columns}
          dataSource={data}
          loading={loading}
          pagination={{
            current: currentPage,
            total: childVariants?.total_count || 0,
            pageSize: 10,
            showSizeChanger: false,
            onChange: (page) => setCurrentPage(page),
            itemRender: (_, type) => {
              if (type === "prev") {
                return <a>Previous</a>;
              }
              if (type === "next") {
                return <a>Next</a>;
              }
              return null;
            },
          }}
          scroll={{ x: 1100, y: 700 }}
        />
        ,
      </div>
    </div>
  );
};

export default ChildVariantDetails;
