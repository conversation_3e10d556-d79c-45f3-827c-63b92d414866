import React from "react";
import { Pagination } from "antd";

interface PaginationComponentProps {
  current: number;
  pageSize: number;
  total: number;
  onChange: (page: number, pageSize?: number) => void;
  showSizeChanger?: boolean; // optional prop
  onShowSizeChange?: (page: number, pageSize?: number) => void;
  pageSizeOptions?: string[];
}

const CommonPagination: React.FC<PaginationComponentProps> = ({
  current,
  pageSize,
  total,
  onChange,
  showSizeChanger = true,
  onShowSizeChange,
  pageSizeOptions,
}) => {
  return (
    <div className="pagination">
      <Pagination
        current={current}
        pageSize={pageSize}
        total={total}
        showSizeChanger={showSizeChanger}
        onShowSizeChange={onShowSizeChange || onChange}
        pageSizeOptions={pageSizeOptions}
        onChange={onChange}
      />
    </div>
  );
};

export default CommonPagination;
