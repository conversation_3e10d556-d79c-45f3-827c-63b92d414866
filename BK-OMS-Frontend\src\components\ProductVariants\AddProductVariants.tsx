import React, { useState, useEffect, useCallback } from "react";
import {
  Form,
  Input,
  Select,
  Button,
  message,
  Popover,
  InputNumber,
  Radio,
  Upload,
  notification,
  Alert,
  Spin,
} from "antd";
import { useNavigate } from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
import { debounce } from "lodash";
import { LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import type { RcFile } from "antd/es/upload";
import axios from "axios";
import useTags, { Tag } from "../../customHooks/usetags";

interface AddProductVariantProps {
  base_product: number;
  code: string;
  sku: string;
  name: string;
  display_name: string;
  description: string;
  product_type: string;
  image_large_url?: string;
  image_thumbnail_url?: string;
  is_crown_product?: boolean;
  loyalty_offer_code?: string;
  combined_sections?: boolean;
  tags?: number[];
  default_pop_variant: number;
}

const AddProductVariants: React.FC = () => {
  const { tags, isLoading, error } = useTags();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [uploading, setUploading] = useState<boolean>(false);
  // const { code } = useParams();
  const isCrownProduct = Form.useWatch("is_crown_product", form);
  const [productData, setProductData] = useState<AddProductVariantProps>({
    base_product: 0,
    code: "",
    sku: "",
    name: "",
    display_name: "",
    description: "",
    product_type: "",
    image_large_url: "",
    image_thumbnail_url: "",
    combined_sections: false,
    tags: [],
    default_pop_variant: 0,
  });

  const [baseProductSearch, setBaseProductSearch] = useState("");
  const [baseProductResults, setBaseProductResults] = useState<any[]>([]);
  const [baseProductPopoverVisible, setBaseProductPopoverVisible] =
    useState(false);
  const [popVariantSearch, setPopVariantSearch] = useState("");
  const [popVariantResults, setPopVariantResults] = useState<any[]>([]);
  const [popVariantPopoverVisible, setPopVariantPopoverVisible] =
    useState(false);
  const [selectedPopVariantId,setSelectedPopVariantId]=useState<number>(0);
  const [selectedBaseProductId, setSelectedBaseProductId] = useState<number>(0);
  const [imagePreview, setImagePreview] = useState<{
    image_large_url?: string;
    image_thumbnail_url?: string;
  }>({});
  //const [tags, setTags] = useState<Tags[]>([]);

  // Debounced function to fetch base products
  const fetchBaseProducts = useCallback(
    debounce(async (searchText: string) => {
      if (searchText.length >= 3) {
        try {
          console.log("Fetching products for search:", searchText);
          const response = await axiosInstance.get(
            `api/menu/products/?search=${searchText}`
          );
          console.log("API Response:", response.data);

          if (response.status === 200) {
            setBaseProductResults(response.data.objects);
            setBaseProductPopoverVisible(response.data.length > 0);
          }
        } catch (error) {
          console.error("Error fetching base products:", error);
        }
      } else {
        setBaseProductResults([]);
        setBaseProductPopoverVisible(false);
      }
    }, 500), // 500ms delay
    []
  );
  const fetchPopVariant = useCallback(
    debounce(async (searchText: string) => {
      if (searchText.length >= 3) {
        try {
          console.log("Fetching products for search:", searchText);
          const response = await axiosInstance.get(
            `api/menu/v2/product-variants-list/?search=${searchText}`
          );
          console.log("API Response:", response.data);

          if (response.status === 200) {
            setPopVariantResults(response.data.objects);
            setPopVariantPopoverVisible(response.data.length > 0);
          }
        } catch (error) {
          console.error("Error fetching base products:", error);
        }
      } else {
        setPopVariantResults([]);
        setPopVariantPopoverVisible(false);
      }
    }, 500), // 500ms delay
    []
  );

  // useEffect(() => {
  //   fetchTags();
  // }, []);

  //fetch the tags
  // const fetchTags = async () => {
  //   try {
  //     const response = await axiosInstance.get("api/menu/tags");
  //     console.log(response);
  //     if (response.status === 200) {
  //       setTags(response.data.objects);
  //     }
  //   } catch (error) {
  //     message.error("something went wrong unable to get tags");
  //   }
  // };
  // Effect to call API when search input changes
  useEffect(() => {
    if (baseProductSearch.length >= 3) {
      fetchBaseProducts(baseProductSearch);
    } else {
      setBaseProductResults([]);
      setBaseProductPopoverVisible(false);
    }
  }, [baseProductSearch, fetchBaseProducts]);
  useEffect(() => {
    // console.log("Updated Base Product Results:", baseProductResults);
    if (baseProductResults.length > 0) {
      setBaseProductPopoverVisible(true);
    }
  }, [baseProductResults]);

  useEffect(() => {
    if (popVariantSearch.length >= 3) {
      fetchPopVariant(popVariantSearch);
    } else {
      setPopVariantResults([]);
      setPopVariantPopoverVisible(false);
    }
  }, [popVariantSearch, fetchPopVariant]);
  useEffect(() => {
    // console.log("Updated Base Product Results:", baseProductResults);
    if (popVariantResults.length > 0) {
      setPopVariantPopoverVisible(true);
    }
  }, [popVariantResults]);


  const onValuesChange = (
    _: Partial<AddProductVariantProps>,
    allValues: AddProductVariantProps
  ) => {
    setProductData({ ...allValues, base_product: selectedBaseProductId,default_pop_variant:selectedPopVariantId });
  };
  const onFinish = async () => {
    try {
      // console.log("Submitting Data:", productData);
      const response = await axiosInstance.post(
        `api/menu/v2/product-variants/${selectedBaseProductId}/`,
        { ...productData, base_product: selectedBaseProductId,default_pop_variant:selectedPopVariantId }
      );
      if (response.status === 201) {
        message.success("Product Variant Created Successfully!");
        navigate(`/variants`);
      }
    } catch (error: any) {
      message.error(
        `Failed to create product: ${
          error.response?.data?.message || "Unknown error"
        }`
      );
    }
  };

  const getUploadProps = (
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => ({
    beforeUpload: (file: RcFile) => {
      handleUpload(file, fieldType);
      return false; // Prevent default upload behavior
    },
    showUploadList: false, // Hide default file list UI
  });
  const uploadToS3 = async (
    uploadData: any,
    file: RcFile,
    _fieldType: "image_large_url" | "image_thumbnail_url"
  ) => {
    setUploading(true);
    try {
      if (!uploadData?.url || !uploadData?.fields) {
        // console.error("Invalid upload data:", uploadData);
        return;
      }

      const formData = new FormData();
      Object.entries(uploadData.fields).forEach(([key, value]) => {
        formData.append(key, value as string);
      });
      formData.append("file", file);

      const response = await axios.post(uploadData.url, formData, {
        headers: { "Content-Type": "multipart/form-data" },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.loaded && progressEvent.total) {
            // const percent = Math.round(
            //   (progressEvent.loaded / progressEvent.total) * 100
            // );
            // setUploadProgress((prev) => ({ ...prev, [file.name]: percent }));
          }
        },
      });

      // setUploadSuccess((prev) => ({ ...prev, [file.name]: true }));
      // console.log("key:", key);
      notification.success({
        message: "Upload Successful",
        description: `${file.name} uploaded successfully.`,
      });
      console.log("uploads3:", response);

      // await updateProductImage(uploadData.key, fieldType);
    } catch (error) {
      // setUploadSuccess((prev) => ({ ...prev, [file.name]: false }));

      notification.error({
        message: "Upload Failed",
        description: `Failed to upload ${file.name}.`,
      });
    } finally {
      setUploading(false);
    }
  };
  const getPresignedUrl = async (file: RcFile) => {
    try {
      // console.log("Fetching presigned URL for:", file.name);
      const response = await axiosInstance.post(
        "/api/utilities/get-file-upload-url/",
        {
          file_name: file.name,
          file_type: "thumbnail",
        }
      );

      if (
        !response.data.url ||
        !response.data.url.url ||
        !response.data.url.fields
      ) {
        return null;
      }

      const fileKey = response.data?.url?.fields?.key;
      // console.log("fileKey:",fileKey);
      // console.log("typeOf",typeof(fileKey));

      if (!fileKey) {
        return null;
      }

      return {
        url: response.data.url.url, // Extracting correct URL
        fields: response.data.url.fields, // Extracting correct fields
        key: response.data?.url?.fields?.key,
      };
    } catch (error) {
      notification.error({
        message: "Error",
        description: `Failed to get presigned URL for ${file.name}.`,
      });
      return null;
    }
  };

  const handleUpload = async (
    file: RcFile,
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => {
    const uploadData = await getPresignedUrl(file);
    if (!uploadData) return;

    await uploadToS3(uploadData, file, fieldType);

    // const s3BaseUrl = uploadData.url;
    const s3Key = uploadData.key;
    const fullUrl = `${s3Key}`;

    // Update form and state
    form.setFieldValue(fieldType, fullUrl);
    setProductData((prev) => ({
      ...prev,
      [fieldType]: fullUrl,
    }));
    setImagePreview((prev) => ({
      ...prev,
      [fieldType]: fullUrl,
    }));
  };

  const uploadButton = (
    <div style={{ border: 0, background: "none", textAlign: "center" }}>
      {uploading ? (
        <LoadingOutlined style={{ fontSize: 24 }} />
      ) : (
        <PlusOutlined style={{ fontSize: 24 }} />
      )}
      <div style={{ marginTop: 8 }}>
        {uploading ? "Uploading..." : "Upload"}
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center">
        <Spin />
      </div>
    );
  }

  if (!isLoading && error) {
    return (
      <div className="d-flex justify-content-center align-items-center">
        <Alert type="error" message={error} />
      </div>
    );
  }

  return (
    <div>
      <h3>Add Product Variants</h3>
      <Form
        form={form}
        name="add_product"
        layout="vertical"
        onFinish={onFinish}
        onValuesChange={onValuesChange}
        initialValues={productData}
        className="add-store-form"
      >
        {/* Base Product Search */}
        <Form.Item
          name="base_product"
          label="Base Product"
          rules={[
            {
              required: true,
              message: "Please select a base product with code",
            },
          ]}
        >
          <Popover
            content={
              <div style={{ maxHeight: "200px", overflowY: "auto" }}>
                {baseProductResults?.length > 0 ? (
                  baseProductResults.map((product) => (
                    <div
                      key={product.id}
                      onClick={() => {
                        console.log("Selected Product:", product);
                        setBaseProductSearch(product.name);
                        setSelectedBaseProductId(product.id);
                        setBaseProductPopoverVisible(false);
                        form.setFieldsValue({ base_product: product.name });
                      }}
                      style={{
                        cursor: "pointer",
                        padding: "5px",
                        borderBottom: "1px solid #ddd",
                      }}
                    >
                      {product.name}
                    </div>
                  ))
                ) : (
                  <div
                    style={{
                      padding: "5px",
                      textAlign: "center",
                      color: "gray",
                    }}
                  >
                    No results found
                  </div>
                )}
              </div>
            }
            title="Search Results"
            trigger="click"
            open={baseProductPopoverVisible && baseProductResults.length > 0}
            onOpenChange={(visible) => {
              console.log("Popover visibility changed:", visible);
              setBaseProductPopoverVisible(visible);
            }}
          >
            <Input
              placeholder="Search Base Product"
              value={baseProductSearch}
              onChange={(e) => {
                console.log("Search Input Changed:", e.target.value);
                setBaseProductSearch(e.target.value);
              }}
            />
          </Popover>
        </Form.Item>
        <Form.Item
          name="default_pop_variant"
          label="Pop Variant"
          rules={[
            {
              required: true,
              message: "Please select a Pop Variant with code or name",
            },
          ]}
        >
          <Popover
            content={
              <div style={{ maxHeight: "200px", overflowY: "auto" }}>
                {popVariantResults?.length > 0 ? (
                  popVariantResults.map((product) => (
                    <div
                      key={product.id}
                      onClick={() => {
                        console.log("Selected Product:", product);
                        setPopVariantSearch(product.name);
                        setSelectedPopVariantId(product.id);
                        setPopVariantPopoverVisible(false);
                        form.setFieldsValue({  default_pop_variant: product.name });
                      }}
                      style={{
                        cursor: "pointer",
                        padding: "5px",
                        borderBottom: "1px solid #ddd",
                      }}
                    >
                      {product.name} {`(${product.code})`}
                    </div>
                  ))
                ) : (
                  <div
                    style={{
                      padding: "5px",
                      textAlign: "center",
                      color: "gray",
                    }}
                  >
                    No results found
                  </div>
                )}
              </div>
            }
            title="Search Results"
            trigger="click"
            open={popVariantPopoverVisible && popVariantResults.length > 0}
            onOpenChange={(visible) => {
              console.log("Popover visibility changed:", visible);
              setPopVariantPopoverVisible(visible);
            }}
          >
            <Input
              placeholder="Search pop variant"
              value={popVariantSearch}
              onChange={(e) => {
                console.log("Search Input Changed:", e.target.value);
                setPopVariantSearch(e.target.value);
              }}
            />
          </Popover>
        </Form.Item>

        <Form.Item
          name="code"
          label="Product Variant Code"
          rules={[{ required: true, message: "Please enter product code" }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="sku"
          label="SKU"
          rules={[{ required: true, message: "Please enter SKU" }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="name"
          label="Product Variant Name"
          rules={[{ required: true, message: "Please enter product name" }]}
        >
          <Input />
        </Form.Item>

        <Form.Item name="display_name" label="Display Name">
          <Input />
        </Form.Item>

        <Form.Item name="description" label="Description">
          <Input.TextArea rows={3} />
        </Form.Item>

        <Form.Item
          name="product_type"
          label="Product Variant Type"
          rules={[{ required: true, message: "Please select a product type" }]}
        >
          <Select placeholder="Select a product type">
            <Select.Option value="ala_carte">Ala Carte</Select.Option>
            <Select.Option value="meal">Meal</Select.Option>
            <Select.Option value="combo">Combo</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item name="tags" label="Tags">
          <Select mode="multiple" placeholder="Select product types" allowClear>
            {tags.map((type: Tag) => (
              <Select.Option key={type.id} value={type.id}>
                {type.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          name="combined_sections"
          label="Combined Sections"
          initialValue={false}
          rules={[{ required: true, message: "Please select an option" }]}
        >
          <Radio.Group>
            <Radio value={true}>True</Radio>
            <Radio value={false}>False</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="is_crown_product"
          label="Is Crown Product"
          initialValue={false}
          rules={[{ required: true, message: "Please select an option" }]}
        >
          <Radio.Group>
            <Radio value={true}>True</Radio>
            <Radio value={false}>False</Radio>
          </Radio.Group>
        </Form.Item>

        {/* Field 3: loyalty_offer_code - shown only if is_crown_product is true */}
        {isCrownProduct === true && (
          <Form.Item
            name="loyalty_offer_code"
            label="Loyalty Offer Code"
            rules={[{ required: true, message: "Please enter offer code" }]}
          >
            <Input placeholder="Enter loyalty offer code" />
          </Form.Item>
        )}

        {/* Field 4: crown_points - shown only if is_crown_product is true */}
        {isCrownProduct === true && (
          <Form.Item
            name="crown_points"
            label="Crown Points"
            rules={[{ required: true, message: "Please enter crown points" }]}
          >
            <InputNumber
              style={{ width: "100%" }}
              placeholder="Enter crown points"
            />
          </Form.Item>
        )}
        {isCrownProduct === true && (
          <Form.Item
            name="price"
            label="Price"
            rules={[{ required: true, message: "Please enter Price" }]}
            className="form-item"
          >
            <InputNumber className="input-field" />
          </Form.Item>
        )}
        <Form.Item label="Upload Large Image">
          <Upload
            listType="picture-card"
            {...getUploadProps("image_large_url")}
          >
            {uploadButton}
          </Upload>
          {imagePreview.image_large_url && (
            <img
              src={`${import.meta.env.VITE_ASSET_URL}/${
                imagePreview.image_large_url
              }`}
              alt="Large Preview"
              style={{ marginTop: 10, maxWidth: "200px", borderRadius: "8px" }}
            />
          )}
        </Form.Item>

        <Form.Item label="Upload Thumbnail">
          <Upload
            listType="picture-card"
            {...getUploadProps("image_thumbnail_url")}
          >
            {uploadButton}
          </Upload>
          {imagePreview.image_thumbnail_url && (
            <img
              src={`${import.meta.env.VITE_ASSET_URL}/${
                imagePreview.image_thumbnail_url
              }`}
              alt="Thumbnail Preview"
              style={{ marginTop: 10, maxWidth: "150px", borderRadius: "8px" }}
            />
          )}
        </Form.Item>

        <Form.Item>
          <Button className="submit-button" type="primary" htmlType="submit">
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddProductVariants;
