import React, { useEffect, useState, useRef, useCallback } from "react";
import { useLocation, useParams } from "react-router-dom";
import { Button, message, InputNumber, Modal } from "antd";
// import { StoreModiferProps } from "../../../types";
import { axiosInstance } from "../../../apiCalls";
import { StoreModiferVariantProps } from "../../../types";
import { CheckOutlined, CloseOutlined, EditOutlined } from "@ant-design/icons";
import "../../Products/Products.css";
import { CSS } from "@dnd-kit/utilities";
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import DataTable from "../../UI/DataTable/DataTable";
// import { useTableFilters } from "../../../customHooks/useFilter";
interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  "data-row-key": string;
}
const DragRow: React.FC<RowProps & { dragEnabled: boolean }> = ({
  dragEnabled,
  ...props
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props["data-row-key"] });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: dragEnabled ? "move" : "default",
    ...(isDragging ? { position: "relative", zIndex: 9999 } : {}),
  };

  return (
    <tr
      {...props}
      ref={setNodeRef}
      style={style}
      {...(dragEnabled ? { ...attributes, ...listeners } : {})}
    />
  );
};
interface CategoryWithKey extends StoreModiferVariantProps {
  key: string;
}

const StoreModiferGroup: React.FC = () => {
  const { storeId, modifierGroupId } = useParams<{
    storeId: string;
    modifierGroupId: string;
  }>();
  // const { currentPage } = useTableFilters();
  // const { pageSize } = useTableFilters();
  // const { setPageSize } = useTableFilters();
  // const { handlePageChange } = useTableFilters();
  // const { handleFilterChange } = useTableFilters();
  // const [totalCount, setTotalCount] = useState<number>(0);
  // const { filters } = useTableFilters();
  const [dragEnabled, setDragEnabled] = useState(false);
  const location = useLocation();
  // const [addNew, _setAddNew] = useState(false);
  const queryParams = new URLSearchParams(location.search);

  //const storeId = queryParams.get("id"); // Access the 'storeid' query parameter
  //const modiferid = queryParams.get("modifer"); // Access the 'id' query parameter
  const name = queryParams.get("name"); // Access the 'name' query parameter
  // const [searchInput, setSearchInput] = useState("");
  // const [StoreModifer, setStoreModifer] = useState(0);
  const [modifiers, setModifiers] = useState<CategoryWithKey[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // const [searchResults, setSearchResults] = useState<StoreModiferProps[]>([]);
  // const [visible, setVisible] = useState(false);
  //const [editingId, setEditingId] = useState<number | null>(null);
  // const [editingPosition, setEditingPosition] = useState<number | null>(null);

  const [editingCategoryId, setEditingCategoryId] = useState<number | null>(
    null
  );
  const [editingId, _setEditingId] = useState<number | null>(null);
  const codeInputRef = useRef<HTMLInputElement>(null);

  const positionInputRef = useRef<HTMLInputElement>(null);
  const [editingPrice, setEditingPrice] = useState<number | null>(null);

  // const navigate = useNavigate();
  // const [form] = Form.useForm();
  // const memoizedFilters = useMemo(() => filters, [filters]);

  // Function to fetch modifiers
  // const getSearchResult = async () => {
  //   // setLoading(true);
  //   try {
  //     const response = await axiosInstance.get(
  //       `api/menu/store-modifiers/${storeId}/?search=${searchInput}`,
  //       {
  //         params: {
  //           page: currentPage,
  //           page_size: pageSize,
  //           ...memoizedFilters,
  //         },
  //       }
  //     );
  //     if (response.status === 200) {
  //       setSearchResults(response.data.objects); // Set the results in the searchResults state
  //     } else {
  //       console.error("Error fetching modifiers", response.status);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching modifiers", error);
  //   } finally {
  //     // setLoading(false);
  //   }
  // };
  // const onSubmit = async (values: any) => {
  //   // Create payload object with product name and position

  //   try {
  //     const payload = {
  //       store_modifier: Number(StoreModifer), // Convert storeProduct to a number
  //       position: Number(values.Postion), // Convert position to a number
  //       modifier_group: Number(modifierGroupId),
  //       // max_qty: Number(values.max_qty),
  //       // default_qty: Number(values.default_qty),
  //       price: Number(values.price),

  //       store: Number(storeId),
  //     };
  //     console.log(payload);
  //     const response = await axiosInstance.post(
  //       `api/menu/store-modifier-variants/${storeId}/`,
  //       payload
  //     );

  //     if (response.status === 201) {
  //       await message.success("Modifier added Successfully!");
  //       window.location.reload();
  //     }
  //     if (response.status === 400) {
  //       message.error("failed to add Modifier");
  //     }
  //   } catch (error: any) {
  //     if (error.response) {
  //       message.error(
  //         `failed to add Modifier: ${
  //           error.response.data.message || "Unknown error"
  //         }`
  //       );
  //     } else {
  //       console.error("failed to add Modifier:", error);
  //       message.error("failed to add Modifier.");
  //     }
  //   }
  // };

  // useEffect(() => {
  //   if (searchInput.length >= 3) {
  //     getSearchResult(); // Trigger the API call only after 3 characters
  //   }
  // }, [searchInput]);
  // const data: StoreModiferVariantProps[] = Array.isArray(modifiers)
  //   ? modifiers.map((modifer) => ({
  //       key: modifer.id,
  //       id: modifer.id,
  //       name: modifer.name || "",
  //       position: modifer.position || 0,
  //       is_active: modifer.is_active,
  //       default_qty: modifer.default_qty ?? 0,
  //       max_qty: modifer.max_qty ?? 0,
  //       store_modifier: modifer.store_modifier ?? 0,
  //       modifier_group: modifer.modifier_group ?? 0,
  //       price: modifer.price,
  //       use_inventory_price: modifer.use_inventory_price,
  //     }))
  //   : [];
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (!dragEnabled || !over || active.id === over.id) return;

    const oldIndex = modifiers.findIndex((c) => c.key === active.id);
    const newIndex = modifiers.findIndex((c) => c.key === over.id);
    const newOrder = arrayMove(modifiers, oldIndex, newIndex);
    setModifiers(newOrder);
  };

  // const handleClearSearch = () => {
  //   setSearch("");
  //   handleFilterChange("search", "");
  // };
  const handleSaveNewOrder = async () => {
    try {
      const updatedmodifiers = modifiers.map((item, idx) => ({
        ...item,
        position: idx + 1,
      }));
      const response = await axiosInstance.patch(
        `/api/menu/store-modifier-variants-position-update/${storeId}/`,
        {
          list: updatedmodifiers,
        }
      );
      if (response.status === 200) {
        message.success("Modifiers reordered successfully.");
        fetchModfiersList();
      }
    } catch {
      message.error("Failed to save new order.");
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 1 } })
  );

  // // Popover content to display search results
  // const popoverContent = (
  //   <div>
  //     <ul>
  //       {searchResults.map((result) => (
  //         <div
  //           key={result.id}
  //           onClick={() => {
  //             setSearchInput(result.name);
  //             setStoreModifer(result.id);

  //             setVisible(false); // Close popover after selection
  //           }}
  //           style={{ cursor: "pointer", padding: "5px" }}
  //         >
  //           {result.name} {`(${result.code})`}
  //         </div>
  //       ))}
  //     </ul>
  //   </div>
  // );
  // const updatePosition = async (categoryId: number, is_active: boolean) => {
  //   try {
  //     const response = await axiosInstance.patch(
  //       `/api/menu/store-modifiers/${storeId}/${categoryId}/`,
  //       {
  //         is_active: is_active,
  //       }
  //     );
  //     if (response.status === 200) {
  //       message.success("Position updated successfully.");
  //       fetchModfiersList();
  //     } else {
  //       message.error("Failed to update position.");
  //     }
  //   } catch (error) {
  //     console.error("Error updating position", error);
  //     message.error("Something went wrong.");
  //   } finally {
  //     // setEditingId(null);
  //     setEditingPosition(null);
  //   }
  // };
  const fetchModfiersList = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/v2/store-modifier-groups/${storeId}/${modifierGroupId}/`
      );
      if (response.status === 200) {
        const sorted = response.data.modifier_variants
          .map((item: any) => ({
            ...item,
            key: item.id.toString(),
          }))
          .sort((a: any, b: any) => a.position - b.position);

        setModifiers(sorted);

        // setLoading(false);
      } else {
        console.log("Error fetching menu data", response.status);
        setModifiers([]);
        // setLoading(false);
      }
    } catch (error) {
      console.error("Error fetching menu data", error);
      // setLoading(false);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (editingId !== null) {
      codeInputRef.current?.focus();
    }
  }, [editingId]);

  //console.log("modiferid", modifierGroupId, "StoreId", storeId);
  useEffect(() => {
    if (modifierGroupId) {
      fetchModfiersList();
    }
  }, [modifierGroupId]);

  const updateStoreModifier = async (
    id: number,
    updates: Partial<{
      position: number;
      price: number;
      is_active: boolean;
      use_inventory_price: boolean;
      is_available:boolean;
    }>
  ) => {
    try {
      setLoading(true);
      const { status } = await axiosInstance.patch(
        `/api/menu/store-modifier-variants/${storeId}/${id}/`,
        updates
      );
      if (status === 200) {
        message.success("Modifier Variant updated successfully.");
        fetchModfiersList();
        return true;
      } else {
        message.error("Failed to update Modifier Variant.");
        return false;
      }
    } catch (err: any) {
      message.error(
        err.response?.data?.message ||
          "An error occurred while updating category."
      );
      return false;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (editingCategoryId !== null) {
      positionInputRef.current?.focus();
    }
  }, [editingCategoryId]);

  const handleSubmitPrice = async (id: number) => {
    if (editingPrice !== null) {
      const ok = await updateStoreModifier(id, {
        price: editingPrice,
      });
      if (ok) {
        setEditingCategoryId(null);
        setEditingPrice(null);
      }
    }
  };

  // Position submit
  // const handleSubmitPosition = async (id: number) => {
  //   if (editingPosition !== null) {
  //     const ok = await updateStoreModifier(id, {
  //       position: editingPosition,
  //     });
  //     if (ok) {
  //       setEditingCategoryId(null);
  //       setEditingPosition(null);
  //     }
  //   }
  // };

  const handleStatusChange = useCallback((id: number, isActive: boolean) => {
    Modal.confirm({
      title: isActive ? "Activate Modifier" : "Deactivate Modifier",
      content: isActive
        ? "Are you sure you want to activate this Modifier?"
        : "Are you sure you want to deactivate this Modifier?",
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        await updateStoreModifier(id, {
          is_active: isActive,
        });
      },
    });
  }, []);
    const handleAvalabiltyChange = useCallback((id: number, isAvailable: boolean) => {
    Modal.confirm({
      title: isAvailable ? "Available Modifier" : "Unavailable Modifier",
      content: isAvailable
        ? "Are you sure you want to available this Modifier?"
        : "Are you sure you want to available this Modifier?",
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: async () => {
        await updateStoreModifier(id, {
          is_available: isAvailable,
        });
      },
    });
  }, []);
  const useInventoryPriceChange = useCallback(
    (id: number, isActive: boolean) => {
      Modal.confirm({
        title: isActive
          ? "Activate Inventory Price"
          : "Deactivate Inventory Price",
        content: isActive
          ? "Are you sure you want to activate Inventory Price?"
          : "Are you sure you want to deactivate Inventory Price?",
        okText: "Yes",
        cancelText: "No",
        className: "custom-modal",
        okButtonProps: { className: "custom-modal-ok-button" },
        cancelButtonProps: { className: "custom-modal-cancel-button" },
        onOk: async () => {
          await updateStoreModifier(id, {
            use_inventory_price: isActive,
          });
        },
      });
    },
    []
  );
  useEffect(() => {
    console.log(modifiers);
  }, [dragEnabled]);

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "30%",
      // render: (text: string, record: CategoryProductProps) => (
      //   <span>
      //     {/* <Link to={`./${record.id}`}>{text}</Link> */}
      //   </span>
      // ),
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
      render: (_: any, record: StoreModiferVariantProps) => (
        <>
          {record.position}
          <Button
            type="link"
            onClick={() => {
              setDragEnabled(true);
              message.info("Drag and drop is now enabled.");
            }}
          >
            <EditOutlined className="btn-edit-pencil" />
          </Button>
          {dragEnabled && (
            <Button
              icon={<CheckOutlined />}
              onClick={() => {
                setDragEnabled(false);
                handleSaveNewOrder();
              }}
            />
          )}
        </>
      ),
    },
    {
      title: "Price",
      dataIndex: "price",
      key: "price",

      render: (price: number, record: StoreModiferVariantProps) => {
        const isEditing =
          editingCategoryId === record.id && editingPrice !== null;
        return isEditing ? (
          <>
            <InputNumber
              ref={positionInputRef}
              value={editingPrice!}
              onChange={(v) => setEditingPrice(v)}
              onPressEnter={() => handleSubmitPrice(record.id)}
              disabled={loading}
              style={{ width: 100, marginRight: 8 }}
            />
            <Button
              icon={<CheckOutlined />}
              onClick={() => handleSubmitPrice(record.id)}
              loading={loading}
            />
            <Button
              icon={<CloseOutlined />}
              onClick={() => {
                // setEditingCategoryId(null);
                setEditingPrice(null);
              }}
              style={{ marginLeft: 4 }}
            />
          </>
        ) : (
          <>
            <span>{price}</span>
            <Button
              type="link"
              onClick={() => {
                setEditingCategoryId(record.id);
                setEditingPrice(price);
              }}
            >
              <EditOutlined className="btn-edit-pencil" />
            </Button>
          </>
        );
      },
    },
    {
      title: "Use Inventory Price ",
      dataIndex: "use_inventory_price",
      key: "use_inventory_price",
      width: "12%",
      fixed: "right" as "right",
      render: (isActive: boolean, record: StoreModiferVariantProps) => (
        <div className="d-flex">
          <div
            className={`switch-button ${isActive ? "checked" : ""}`}
            onClick={() => useInventoryPriceChange(record.id, !isActive)}
          >
            <span className="switch-label">
              {isActive ? <CheckOutlined /> : <CloseOutlined />}
            </span>
            <div className="switch-handle"></div>
          </div>
        </div>
      ),
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      width: "12%",
      fixed: "right" as "right",
      render: (isActive: boolean, record: StoreModiferVariantProps) => (
        <div className="d-flex">
          <div
            className={`switch-button ${isActive ? "checked" : ""}`}
            onClick={() => handleStatusChange(record.id, !isActive)}
          >
            <span className="switch-label">
              {isActive ? <CheckOutlined /> : <CloseOutlined />}
            </span>
            <div className="switch-handle"></div>
          </div>
        </div>
      ),
    },
        {
      title: "Available",
      dataIndex: "is_available",
      key: "is_available",
      width: "12%",
      fixed: "right" as "right",
      render: (isAvailable: boolean, record: StoreModiferVariantProps) => (
        <div className="d-flex">
          <div
            className={`switch-button ${isAvailable ? "checked" : ""}`}
            onClick={() => handleAvalabiltyChange(record.id, !isAvailable)}
          >
            <span className="switch-label">
              {isAvailable ? <CheckOutlined /> : <CloseOutlined />}
            </span>
            <div className="switch-handle"></div>
          </div>
        </div>
      ),
    },
  ];

  return (
    <>
      <div>
        <div className="main-dashboard-buttons">
          {/* <button
          className="typography"
          onClick={() => {
            setAddNew(true);
          }}
        >
          {" "}
          + Add New{" "}
        </button> */}
        </div>
        <div className="container product-card-banner">
          <div className="header products-headers">
            <div className="title">
              {`STORE MODIFIER GROUPS`}{" "}
              <span className="text-base align-middle mx-1">&gt;&gt;</span>{" "}
              {name}
            </div>
            <div className="search-container">
              <div className="search-box" />
              <div className="icon-container">
                <div className="icon-background"></div>
                <div className="icon-dot"></div>
                <div className="icon-overlay"></div>
              </div>

              {/* Search Input with Popover */}
            </div>
          </div>
          <DndContext
            sensors={sensors}
            modifiers={[restrictToVerticalAxis]}
            onDragEnd={onDragEnd}
          >
            <SortableContext
              items={modifiers.map((item) => item.key)}
              strategy={verticalListSortingStrategy}
            >
              <DataTable
                loading={loading}
                columns={columns}
                dataSource={modifiers}
                components={{
                  body: {
                    row: (props: RowProps) => (
                      <DragRow {...props} dragEnabled={dragEnabled} />
                    ),
                  },
                }}
                rowKey="key"
              />
            </SortableContext>
          </DndContext>
        </div>
      </div>
    </>
  );
};

export default StoreModiferGroup;
