import React, { useEffect, useState } from "react";
import { message, Select, Spin, AutoComplete, Card, Form } from "antd";
import type { SelectProps } from "antd";
import { axiosInstance } from "../../../apiCalls";
import { useDebounce } from "../../../customHooks/useDebounce";
import { Button } from "react-bootstrap";

interface StoreDataProps {
  id: number;
  name: string;
  code: string;
  third_party_id: string;
}

interface ConfigSetProps {
  id: number;
  name: string;
  config_set_type: string;
}

const MenuPush: React.FC = () => {
  const [storeOptions, setStoreOptions] = useState<SelectProps["options"]>([]);
  const [configOptions, setConfigOptions] = useState<SelectProps["options"]>(
    []
  );
  const [loadingStores, setLoadingStores] = useState(false);
  const [loadingConfigs, setLoadingConfigs] = useState(false);
  const [selectedStores, setSelectedStores] = useState<StoreDataProps[]>([]);
  const [thirdPartyIds, setThirdPartyIds] = useState<string[]>([]);
  const [selectedConfig, setSelectedConfig] = useState<ConfigSetProps | null>(
    null
  );
  const [storeSearchTerm, setStoreSearchTerm] = useState<string>("");
  const [configSearchValue, setConfigSearchValue] = useState<string>("");

  const debouncedStoreSearch = useDebounce(storeSearchTerm, 400);
  const debouncedConfigSearch = useDebounce(configSearchValue, 400);

  const fetchStores = async (search = "") => {
    setLoadingStores(true);
    try {
      const response = await axiosInstance.get(
        `api/stores/${search ? `?search=${search}` : ""}`
      );
      const options = (response.data.objects || []).map(
        (store: StoreDataProps) => ({
          value: store.id,
          label: `${store.name} (${store.code})`,
          third_party_id: store.third_party_id,
        })
      );
      setStoreOptions(options);
    } catch {
      message.error("Failed to load stores");
    } finally {
      setLoadingStores(false);
    }
  };

  const fetchConfigSets = async (search = "") => {
    setLoadingConfigs(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/config-sets/${search ? `?search=${search}` : ""}`
      );
      const options = (response.data?.objects || []).map((config: any) => {
      const cleanedLabel = config.name.replace(/[\[\]']/g, ""); // removes brackets and single quotes
      return {
        value: `${config.id}`,      // Use ID as unique value
        label: cleanedLabel.trim(), // Display the clean label
        id: config.id,              // Store original ID separately
        config_set_type: config.config_set_type || "",
      };
    });
      setConfigOptions(options);
    } catch {
      message.error("Failed to load config sets");
      setConfigOptions([]);
    } finally {
      setLoadingConfigs(false);
    }
  };

  const generateMenu = async () => {
    try {
      const response = await axiosInstance.post(`api/menu/push-menu/`, {
        stores: thirdPartyIds,
        config_set: selectedConfig?.id,
      });
      if (response.status === 201) {
        message.success(response.data.message || "Menu pushed successfully");
      } else {
        message.error("Something went wrong while pushing menu");
      }
    } catch {
      message.error("Failed to push menu");
    }
  };

  const handleStoreSelect = (values: number[]) => {
    const selected = values
      .map((id) => {
        const store = storeOptions?.find((s) => s?.value === id);
        return store
          ? {
              id,
              name: store.label as string,
              code: "",
              third_party_id: store.third_party_id || "",
            }
          : null;
      })
      .filter(Boolean) as StoreDataProps[];

    setSelectedStores(selected);
    setThirdPartyIds(selected.map((store) => store.third_party_id));
  };

  // const handleConfigSelect = (_value: string, option: any) => {
  //   setSelectedConfig({
  //     id: option.value,
  //     name: option.label,
  //     config_set_type: option.config_set_type || "",
  //   });
  //   setConfigSearchValue(option.label);
  // };



 const handleConfigSelect = (value: string, option: any) => {
  console.log("handleConfigSelect called with:", { value, option });

  const label = String(option.label ?? "");
  console.log("Setting label in input:", label);

  setSelectedConfig({
    id: Number(option.id),        // Use the separate id field
    name: label,
    config_set_type: option.config_set_type || "",
  });

  // The value is already the label, so AutoComplete will display it correctly
  setConfigSearchValue(label);
  console.log("Set config search value to:", label);
};




  useEffect(() => {
    fetchStores();
  }, []);

  useEffect(() => {
    if (debouncedStoreSearch.length >= 0) fetchStores(debouncedStoreSearch);
  }, [debouncedStoreSearch]);

  useEffect(() => {
    if (debouncedConfigSearch.length >= 3) {
      fetchConfigSets(debouncedConfigSearch);
    } else {
      setConfigOptions([]);
    }
  }, [debouncedConfigSearch]);

  return (
    <div>
      <Card className="mt-3" title="Push Menu" bordered={true}>
        <Form
          //form={form}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 14 }}
          style={{ maxWidth: 600 }}
          onFinish={generateMenu}
        >
          <Form.Item
            label="Select Config Set"
            name="config_set"
            rules={[{ required: true, message: "Please select a config set" }]}
          >
            <AutoComplete
              placeholder="Search and select config set"
              value={configSearchValue}
              onSearch={setConfigSearchValue}
              onSelect={handleConfigSelect}
              onClear={() => {
                setSelectedConfig(null);
                setConfigSearchValue("");
                setConfigOptions([]);
              }}
              style={{ width: "100%", marginBottom: "10px" }}
              options={configOptions}
              notFoundContent={
                loadingConfigs &&
                (configSearchValue.length >= 3 ||
                  configSearchValue.length === 0) ? (
                  <Spin size="small" />
                ) : configSearchValue.length > 0 &&
                  configSearchValue.length < 3 ? (
                  "Type at least 3 characters to search"
                ) : configOptions?.length === 0 ? (
                  "No config sets found"
                ) : null
              }
              allowClear
            />
          </Form.Item>
          <Form.Item
            label="Select Stores"
            name="stores"
            rules={[
              { required: true, message: "Please select at least one store" },
            ]}
          >
            <Select
              mode="multiple"
              showSearch
              placeholder="Search and select stores"
              notFoundContent={
                loadingStores ? <Spin size="small" /> : "No stores"
              }
              filterOption={false}
              onSearch={setStoreSearchTerm}
              onChange={handleStoreSelect}
              style={{ width: "100%" }}
              options={storeOptions}
            />
          </Form.Item>
          <Form.Item label={null}>
            <div className="d-flex justify-content-end mt-5">
              {selectedStores.length > 0 && selectedConfig && (
                <Button className="btn-save" onClick={generateMenu}>
                  Push Menu
                </Button>
              )}
            </div>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default MenuPush;
