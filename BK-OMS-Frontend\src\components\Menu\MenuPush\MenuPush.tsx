import React, { useEffect, useRef, useState } from "react";
import { message, Select, Spin, AutoComplete } from "antd";
import type { SelectProps } from "antd";
import { axiosInstance } from "../../../apiCalls";

interface StoreDataProps {
  id: number;
  name: string;
  code: string;
  third_party_id: string;
}

interface ConfigSetProps {
  id: number;
  name: string;
  config_set_type: string;
}

const MenuPush: React.FC = () => {
  const [storeOptions, setStoreOptions] = useState<SelectProps["options"]>([]);
  const [configOptions, setConfigOptions] = useState<SelectProps["options"]>(
    []
  );
  const [loadingStores, setLoadingStores] = useState(false);
  const [loadingConfigs, setLoadingConfigs] = useState(false);
  const [selectedStores, setSelectedStores] = useState<StoreDataProps[]>([]);
  const [thirdPartyIds, setThirdPartyIds] = useState<string[]>([]);
  const [selectedConfig, setSelectedConfig] = useState<ConfigSetProps | null>(
    null
  );
  const [configSearchValue, setConfigSearchValue] = useState<string>("");

  const isMounted = useRef(true);
  const storeSearchRef = useRef<number | null>(null);
  const configSearchRef = useRef<number | null>(null);

  const fetchStores = async (search = "") => {
    setLoadingStores(true);
    try {
      const response = await axiosInstance.get(
        `api/stores/${search ? `?search=${search}` : ""}`
      );
      const storeOptions = (response.data.objects || []).map(
        (store: StoreDataProps) => ({
          value: store.id,
          label: `${store.name} (${store.code})`,
          third_party_id: store.third_party_id,
        })
      );
      if (isMounted.current) setStoreOptions(storeOptions);
    } catch (error) {
      message.error("Failed to load stores");
    } finally {
      setLoadingStores(false);
    }
  };

  const fetchConfigSets = async (search = "") => {
    try {
      const response = await axiosInstance.get(
        `api/menu/config-sets/${search ? `?search=${search}` : ""}`
      );
      const configOptions = (response.data || []).map(
        (config: ConfigSetProps) => ({
          value: config.id,
          label: config.name,
          config_set_type: config.config_set_type,
        })
      );
      if (isMounted.current) setConfigOptions(configOptions);
    } catch (error) {
      message.error("Failed to load config sets");
      if (isMounted.current) setConfigOptions([]);
    } finally {
      setLoadingConfigs(false);
    }
  };

  const generateMenu = async () => {
    try {
      const response = await axiosInstance.post(`api/menu/push-menu/`, {
        stores: thirdPartyIds,
        config_set: selectedConfig?.id,
      });

      if (response.status === 201) {
        message.success(response.data.message || "Menu pushed successfully");
      } else {
        message.error("Something went wrong while pushing menu");
      }
    } catch (error) {
      message.error("Failed to push menu");
    }
  };

  const handleStoreSelect = (values: number[]) => {
    const selected = values
      .map((id) => {
        const store = storeOptions?.find((s) => s?.value === id);
        return store
          ? {
              id,
              name: store.label as string,
              code: "",
              third_party_id: store.third_party_id || "",
            }
          : null;
      })
      .filter(Boolean) as StoreDataProps[];

    setSelectedStores(selected);
    setThirdPartyIds(selected.map((store) => store.third_party_id));
  };

  const handleConfigSelect = (_value: string, option: any) => {
    if (option) {
      setSelectedConfig({
        id: option.value as number,
        name: option.label as string,
        config_set_type: option.config_set_type || "",
      });
      setConfigSearchValue(option.label as string);
    }
  };

  const handleStoreSearch = (search: string) => {
    if (storeSearchRef.current) {
      clearTimeout(storeSearchRef.current);
    }
    storeSearchRef.current = setTimeout(() => {
      fetchStores(search);
    }, 400);
  };

  const handleConfigSearch = (search: string) => {
    setConfigSearchValue(search);

    // Clear previous timeout
    if (configSearchRef.current) {
      clearTimeout(configSearchRef.current);
    }

    // Stop any existing loading state first
    setLoadingConfigs(false);

    // Only search if 3 or more characters, or if empty (to show all)
    if (search.length >= 3 || search.length === 0) {
      setLoadingConfigs(true);
      configSearchRef.current = setTimeout(() => {
        fetchConfigSets(search);
      }, 400);
    } else {
      // Clear options if less than 3 characters (but not empty)
      setConfigOptions([]);
    }
  };

  useEffect(() => {
    fetchStores();
    return () => {
      isMounted.current = false;
      if (storeSearchRef.current) clearTimeout(storeSearchRef.current);
      if (configSearchRef.current) clearTimeout(configSearchRef.current);
    };
  }, []);

  return (
    <div>
      <AutoComplete
        placeholder="Search and select config set"
        value={configSearchValue}
        onSearch={handleConfigSearch}
        onSelect={handleConfigSelect}
        onClear={() => {
          setSelectedConfig(null);
          setConfigSearchValue("");
          setConfigOptions([]);
          setLoadingConfigs(false);
        }}
        style={{ width: "100%", marginBottom: "10px" }}
        options={configOptions}
        notFoundContent={
          loadingConfigs ? <Spin size="small" /> : "No config sets found"
        }
        allowClear
      />

      <Select
        mode="multiple"
        showSearch
        placeholder="Search and select stores"
        notFoundContent={loadingStores ? <Spin size="small" /> : "No stores"}
        filterOption={false}
        onSearch={handleStoreSearch}
        onChange={handleStoreSelect}
        style={{ width: "100%" }}
        options={storeOptions}
      />

      {selectedStores.length > 0 && selectedConfig && (
        <button
          className="bg-[#FF8732] text-white px-4 py-2 mt-4 rounded-lg"
          onClick={generateMenu}
        >
          Push Menu
        </button>
      )}
    </div>
  );
};

export default MenuPush;
