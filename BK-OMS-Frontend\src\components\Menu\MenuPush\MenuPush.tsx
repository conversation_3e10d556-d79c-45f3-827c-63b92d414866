import React, { useEffect, useState } from "react";
import { message, Select, Spin } from "antd";
import type { SelectProps } from "antd";
import { axiosInstance } from "../../../apiCalls";

interface StoreDataProps {
  id: number;
  name: string;
  code: string;
  third_party_id: string;
}

interface ConfigSetProps {
  id: number;
  name: string;
  config_set_type: string;
}

const MenuPush: React.FC = () => {
  const [storeOptions, setStoreOptions] = useState<SelectProps["options"]>([]);
  const [configOptions, setConfigOptions] = useState<SelectProps["options"]>(
    []
  ); // Always initialized as an empty array
  const [loadingStores, setLoadingStores] = useState(false);
  const [loadingConfigs, setLoadingConfigs] = useState(false);
  const [selectedStores, setSelectedStores] = useState<StoreDataProps[]>([]);
  const [thirdPartyIds, setThirdPartyIds] = useState<string[]>([]);
  const [selectedConfig, setSelectedConfig] = useState<ConfigSetProps | null>(
    null
  );

  const fetchStores = async (search?: string) => {
    setLoadingStores(true);
    try {
      const response = await axiosInstance.get(
        `api/stores/${search ? `?search=${search}` : ""}`
      );
      const data = response.data;
      const storeOptions = (data.objects || []).map(
        (store: StoreDataProps) => ({
          value: store.id,
          label: `${store.name} (${store.code})`,
          third_party_id: store.third_party_id,
        })
      );
      setStoreOptions(storeOptions);
    } catch (error) {
      console.error("Error fetching stores:", error);
    } finally {
      setLoadingStores(false);
    }
  };

  const fetchConfigSets = async () => {
    setLoadingConfigs(true);
    try {
      const response = await axiosInstance.get("api/menu/config-sets/");
      const data = response.data;
      const configOptions = (data || []).map(
        (config: ConfigSetProps) => ({
          value: config.id,
          label: config.name,
          config_set_type: config.config_set_type,
        })
      );
      setConfigOptions(configOptions);
    // console.log(response);
    } catch (error) {
      console.error("Error fetching config sets:", error);
    } finally {
      setLoadingConfigs(false);
    }
  };

  useEffect(() => {
    fetchStores(); // Fetch all stores initially
    fetchConfigSets(); // Fetch all config sets initially
  }, []);
  

  const generateMenu = async () => {
    try {
      const response = await axiosInstance.post(`api/menu/push-menu/`, {
        stores: thirdPartyIds,
        config_set: selectedConfig?.id,
      });
      console.log(response);
      if (response.status === 201) {
        message.success(response.data.message);
      } else {
        message.error("Something went wrong");
      }
    } catch (error) {
      message.error("Something went wrong");
    }
  };

  const handleStoreSelect = (values: number[]) => {
    const stores = values
      .map((value) => {
        const store = storeOptions?.find((option) => option.value === value);
        return store
          ? {
              id: value,
              name: store.label as string,
              code: "",
              third_party_id: store.third_party_id || "",
            }
          : null;
      })
      .filter(Boolean) as StoreDataProps[];
    setSelectedStores(stores);
    setThirdPartyIds(stores.map((store) => store.third_party_id));
  };
  const handleConfigSelect = (value: number) => {
    const config = configOptions?.find((option) => option.value === value);
    if (config) {
      setSelectedConfig({
        id: config.value as number, // Ensure it's a number
        name: config.label as string,
        config_set_type: config.config_set_type ?? "",
      });
    }
  };

  useEffect(() => {
    console.log("Selected Stores:", selectedStores);
    console.log("Selected Config:", selectedConfig);
  }, [selectedStores, selectedConfig]);

  return (
    <div>
      <Select
        showSearch
        placeholder="Select Config Set"
        notFoundContent={loadingConfigs ? <Spin size="small" /> : null}
        filterOption={false}
        onChange={handleConfigSelect}
        style={{ width: "100%", marginTop: "10px" }}
        options={configOptions}
      />
      <Select
        mode="multiple"
        showSearch
        placeholder="Search and select stores"
        notFoundContent={loadingStores ? <Spin size="small" /> : null}
        filterOption={false}
        onSearch={fetchStores}
        onChange={handleStoreSelect}
        style={{ width: "100%" }}
        options={storeOptions}
      />

      {selectedStores.length > 0 && selectedConfig && (
        <button
          className="bg-[#FF8732] text-white px-4 py-2 mt-4 rounded-lg"
          onClick={generateMenu}
        >
          Push Menu
        </button>
      )}
    </div>
  );
};

export default MenuPush;
