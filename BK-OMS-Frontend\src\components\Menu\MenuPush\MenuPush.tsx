import React, { useEffect, useRef, useState } from "react";
import { message, Select, Spin } from "antd";
import type { SelectProps } from "antd";
import { axiosInstance } from "../../../apiCalls";

interface StoreDataProps {
  id: number;
  name: string;
  code: string;
  third_party_id: string;
}

interface ConfigSetProps {
  id: number;
  name: string;
  config_set_type: string;
}

const MenuPush: React.FC = () => {
  const [storeOptions, setStoreOptions] = useState<SelectProps["options"]>([]);
  const [configOptions, setConfigOptions] = useState<SelectProps["options"]>([]);
  const [loadingStores, setLoadingStores] = useState(false);
  const [loadingConfigs, setLoadingConfigs] = useState(false);
  const [selectedStores, setSelectedStores] = useState<StoreDataProps[]>([]);
  const [thirdPartyIds, setThirdPartyIds] = useState<string[]>([]);
  const [selectedConfig, setSelectedConfig] = useState<ConfigSetProps | null>(null);

  const isMounted = useRef(true);
  const storeSearchRef = useRef<NodeJS.Timeout | null>(null);
  const configLoadedRef = useRef(false); // To prevent multiple loads

  const fetchStores = async (search = "") => {
    setLoadingStores(true);
    try {
      const response = await axiosInstance.get(`api/stores/${search ? `?search=${search}` : ""}`);
      const storeOptions = (response.data.objects || []).map((store: StoreDataProps) => ({
        value: store.id,
        label: `${store.name} (${store.code})`,
        third_party_id: store.third_party_id,
      }));
      if (isMounted.current) setStoreOptions(storeOptions);
    } catch (error) {
      message.error("Failed to load stores");
    } finally {
      if (isMounted.current) setLoadingStores(false);
    }
  };

  const fetchConfigSets = async () => {
    if (configLoadedRef.current) return;
    configLoadedRef.current = true; // prevent duplicate calls

    setLoadingConfigs(true);
    try {
      const response = await axiosInstance.get("api/menu/config-sets/");
      const configOptions = (response.data || []).map((config: ConfigSetProps) => ({
        value: config.id,
        label: config.name,
        config_set_type: config.config_set_type,
      }));
      if (isMounted.current) setConfigOptions(configOptions);
    } catch (error) {
      message.error("Failed to load config sets");
    } finally {
      if (isMounted.current) setLoadingConfigs(false);
    }
  };

  const generateMenu = async () => {
    try {
      const response = await axiosInstance.post(`api/menu/push-menu/`, {
        stores: thirdPartyIds,
        config_set: selectedConfig?.id,
      });

      if (response.status === 201) {
        message.success(response.data.message || "Menu pushed successfully");
      } else {
        message.error("Something went wrong while pushing menu");
      }
    } catch (error) {
      message.error("Failed to push menu");
    }
  };

  const handleStoreSelect = (values: number[]) => {
    const selected = values
      .map((id) => {
        const store = storeOptions.find((s) => s?.value === id);
        return store
          ? {
              id,
              name: store.label as string,
              code: "",
              third_party_id: store.third_party_id || "",
            }
          : null;
      })
      .filter(Boolean) as StoreDataProps[];

    setSelectedStores(selected);
    setThirdPartyIds(selected.map((store) => store.third_party_id));
  };

  const handleConfigSelect = (id: number) => {
    const config = configOptions?.find((c) => c?.value === id);
    if (config) {
      setSelectedConfig({
        id: config.value as number,
        name: config.label as string,
        config_set_type: config.config_set_type || "",
      });
    }
  };

  const handleStoreSearch = (search: string) => {
    if (storeSearchRef.current) {
      clearTimeout(storeSearchRef.current);
    }
    storeSearchRef.current = setTimeout(() => {
      fetchStores(search);
    }, 400);
  };

  useEffect(() => {
    fetchStores(); // Only stores are loaded initially
    return () => {
      isMounted.current = false;
      if (storeSearchRef.current) clearTimeout(storeSearchRef.current);
    };
  }, []);

  return (
    <div>
      <Select
        showSearch
        placeholder="Select Config Set"
        notFoundContent={loadingConfigs ? <Spin size="small" /> : "No config sets"}
        filterOption={false}
        onDropdownVisibleChange={(open) => {
          if (open) fetchConfigSets();
        }}
        onChange={handleConfigSelect}
        style={{ width: "100%", marginBottom: "10px" }}
        options={configOptions}
      />

      <Select
        mode="multiple"
        showSearch
        placeholder="Search and select stores"
        notFoundContent={loadingStores ? <Spin size="small" /> : "No stores"}
        filterOption={false}
        onSearch={handleStoreSearch}
        onChange={handleStoreSelect}
        style={{ width: "100%" }}
        options={storeOptions}
      />

      {selectedStores.length > 0 && selectedConfig && (
        <button
          className="bg-[#FF8732] text-white px-4 py-2 mt-4 rounded-lg"
          onClick={generateMenu}
        >
          Push Menu
        </button>
      )}
    </div>
  );
};

export default MenuPush;
