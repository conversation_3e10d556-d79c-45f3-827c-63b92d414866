import { useEffect, useState } from "react";
import { Form, InputNumber, Button,  message, Switch, Select } from "antd";
import { axiosInstance } from "../../../apiCalls";
import { useNavigate, useParams } from "react-router-dom";
import "../addStore/AddStore.css";

interface AddStoreModifierProps {
  price: number;
  is_active: boolean;
  default_qty: number;
  max_qty: number;
  store: number;
  modifier: number;
}

interface ModifierListProps {
  id: number;
  product: string;
  name:string;
}

const AddStoreModifiers = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [modifierList, setModifierList] = useState<ModifierListProps[]>([]);
  const [modifierData, setModifierData] = useState<AddStoreModifierProps>({
    price: 0.0,
    is_active: true,
    default_qty: 1,
    max_qty: 1,
    store: Number(id),
    modifier: 0,
  });

  useEffect(() => {
    // Fetch the list of available modifiers
    const fetchModifiers = async () => {
      try {
        const response = await axiosInstance.get(`api/menu/modifiers/`);
        if (response.status === 200) {
          setModifierList(response.data.objects);
        } else {
          message.error("Error fetching modifier list");
          navigate("/stores");
        }
      } catch (error: any) {
        console.error("Error fetching modifiers:", error);
        message.error("Failed to fetch modifiers.");
      }
    };
    fetchModifiers();
  }, []);

  const onValuesChange = (
    _: Partial<AddStoreModifierProps>,
    allValues: AddStoreModifierProps
  ) => {
    setModifierData({ ...allValues, store: Number(id) });
  };

  const onFinish = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.post(
        `api/menu/store-modifiers/${id}/`,
        modifierData
      );
      if (response.status === 201) {
        message.success("Store Modifier Created Successfully!");
        navigate(`/stores/${id}?tab=Store_Modifiers`);
      }
    } catch (error: any) {
      console.error("Error creating store modifier:", error);
      message.error("Failed to create store modifier.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h3>Add Store Modifier</h3>
      <Form
        form={form}
        name="add_store_modifier"
        layout="vertical"
        onFinish={onFinish}
        onValuesChange={onValuesChange}
        initialValues={modifierData}
        className="add-store-form"
      >
        {/* Modifier Selection */}
        <Form.Item
          name="modifier"
          label="Modifier"
          rules={[{ required: true, message: "Please select a modifier" }]}
        >
          <Select placeholder="Select Modifier" className="input-field">
            {modifierList.map((modifier) => (
              <Select.Option key={modifier.id} value={modifier.id}>
                {modifier.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>

        {/* Price */}
        <Form.Item
          name="price"
          label="Price"
          rules={[{ required: true, message: "Please enter price" }]}
        >
          <InputNumber min={0} step={0.01} className="input-field" />
        </Form.Item>

        {/* Default Quantity */}
        <Form.Item
          name="default_qty"
          label="Default Quantity"
          rules={[{ required: true, message: "Please enter default quantity" }]}
        >
          <InputNumber min={1} max={10} className="input-field" />
        </Form.Item>

        {/* Max Quantity */}
        <Form.Item
          name="max_qty"
          label="Max Quantity"
          rules={[{ required: true, message: "Please enter max quantity" }]}
        >
          <InputNumber min={1} max={10} className="input-field" />
        </Form.Item>

        {/* Is Active */}
        <Form.Item name="is_active" label="Active" valuePropName="checked">
          <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
        </Form.Item>

        {/* Submit Button */}
        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading}>
            Submit
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AddStoreModifiers;
