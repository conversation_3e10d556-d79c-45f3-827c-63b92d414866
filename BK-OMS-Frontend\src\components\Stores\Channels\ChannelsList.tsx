import React, { useEffect, useState } from "react";
import { Card, message } from "antd";
import { ChannelsListProps } from "../../../types";
import { axiosInstance } from "../../../apiCalls";
import { useParams } from "react-router-dom";
import { useNavigate } from "react-router-dom";

const { Meta } = Card;

// interface Chanels {
//   objects: ChannelsListProps[];
//   page_size: number;
//   current_page: number;
//   total_pages: number;
//   next_page: number | null;
//   previous_page: number | null;
//   total_count: number;
// }

const ChannelsList: React.FC = () => {
  const [data, setData] = useState<ChannelsListProps[]>([]);
  // const [currentPage, setCurrentPage] = useState(1);
  const navigate = useNavigate();
  const { id } = useParams();

  const getChannels = async () => {
    try {
      const response = await axiosInstance.get(
        `api/menu/list-channel-services/${id}`,
        // {
        //   params: { page },
        // }
      );
      if (response.status === 200) {
        setData(response.data.objects);
      }
    } catch (error) {
      message.error("something went wrong");
    }
  };
  useEffect(() => {
    getChannels();
  }, []);

  return (
    <div
      style={{
        display: "flex",
        gap: "16px",
        justifyContent: "center",
        flexWrap: "wrap",
      }}
    >
      {data.map((item, index) => (
        <Card onClick={()=>{
            navigate(`/store/channel/?storeId=${id}&channel=${item.id}`)
            
        }}
          key={index}
          hoverable
          style={{ width: 240,}} // Decrease height here
          cover={
            item.channel_image_url ? (
              <img
                alt="example"
                src={item.channel_image_url}
                // Adjust image height
              />
            ) : null
          }
        >
          <Meta title={item.channel_name} description={item.service_name} />
        </Card>
      ))}
    </div>
  );
};

export default ChannelsList;
