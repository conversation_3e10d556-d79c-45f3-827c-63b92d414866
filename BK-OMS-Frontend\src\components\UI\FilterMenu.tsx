import React from "react";
import { Menu } from "antd";
import { FilterDropdownProps } from "antd/lib/table/interface";

interface Option {
  label: string;
  value: string | number;
}

interface FilterMenuProps extends FilterDropdownProps {
  filterKey: string;
  options: Option[];
  handleFilterChange: (key: string, value: string) => void;
}

const FilterMenu: React.FC<FilterMenuProps> = ({
  setSelectedKeys,
  selectedKeys,
  confirm,
  filterKey,
  options,
  handleFilterChange,
}) => {
  return (
    <Menu
      onClick={({ key }) => {
        const stringKey = String(key);
        setSelectedKeys(stringKey ? [stringKey] : []);
        confirm();
        handleFilterChange(filterKey, stringKey);
      }}
      selectedKeys={selectedKeys.map((key) => String(key))}
      items={[
        { key: "All", label: "All" },
        ...options.map((option) => ({
          key: String(option.value),
          label: option.label,
        })),
      ]}
    />
  );
};

export default FilterMenu;
