@import "tailwindcss";
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

/* ant table  */

.ant-table-filter-column {
  font-family: "Poppins";
}
td.ant-table-cell {
  font-family: "Poppins";
  font-size: 14px !important;
}
td.ant-table-cell span {
  /* background: none !important; */
  border: none;
  box-shadow: none !important;
  font-family: "Poppins";
}
td.ant-table-cell span {
  /* background: none !important; */
  border: none;
  box-shadow: none !important;
  font-family: "Poppins";
  font-weight: normal !important;
  font-size: 12px !important;
  /* text-transform: lowercase !important; */
}

/* @keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
} */

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.backButton {
  margin-bottom: 2;
  display: flex;
  align-items: center;
  background-color: #ff8732;
  color: #fff;
}
.backButton:hover {
  margin-bottom: 2;
  display: flex;
  align-items: center;
  background-color: #ff8732 !important;
  color: #fff !important;
  filter: drop-shadow(0 0 2em #ff8732aa);
}

/* switch Button */
.d-flex {
  display: flex;
  align-items: center;
  gap: 15px;
}

.switch-button {
  width: 70px !important;
  height: 34px !important;
  background-color: #d9d9d9 !important;
  border-radius: 17px !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  transition: background-color 0.3s ease-in-out;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  padding: 0 10px;
  font-size: 14px;
  font-weight: bold;
  color: white;
  overflow: hidden;
}

.switch-button .switch-handle {
  width: 28px;
  height: 28px;
  background-color: white;
  border-radius: 50%;
  position: absolute;
  top: 3px;
  left: 3px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease-in-out;
}

.switch-button.checked {
  background-color: #ff8732 !important;
}

.switch-button.checked .switch-handle {
  transform: translateX(36px);
}

.switch-label {
  position: absolute;
  width: 100%;
  text-align: center;
  z-index: 1;
  font-size: 14px;
  font-weight: bold;
  color: black;
  cursor: pointer;
}

.switch-button.checked .switch-label {
  right: 10px;
  color: white;
  font-family: "Poppins", sans-serif;
}

.switch-button:not(.checked) .switch-label {
  left: 10px;
  color: white;
  font-family: "Poppins", sans-serif;
}

/* switch button pop modal button Yes and No */
.custom-modal .ant-modal-content {
  border-radius: 8px;
  padding: 16px;
}

.custom-modal .ant-modal-header {
  background-color: #ff8732;
  color: white;
  border-radius: 8px 8px 0 0;
}

.custom-modal .ant-modal-title {
  color: white;
  font-weight: bold;
}

.custom-modal-ok-button {
  background-color: #ff8732 !important;
  border-color: #ff8732 !important;
  color: white !important;
}

.custom-modal-cancel-button {
  background-color: #f0f0f0 !important;
  color: black !important;
}

.add-item-bt {
  background: #ff8732 !important;
  height: 40px;
  font-family: "Poppins";
  font-weight: 400 !important;
  font-size: 14px !important;
  padding: 0 20px;
  margin-bottom: 6px;
  margin-top: 30px;
}

/* save & cancel button */
.btn-save {
  background: #ff8732 !important; /* Save button color */
  height: 40px;
  font-family: "Poppins";
  font-weight: 400 !important;
  font-size: 14px !important;
  padding: 0 20px;
  margin-bottom: 6px;
  border: none;
  color: white;
}

.btn-save:hover {
  background: #e76e1e !important; /* Darker shade on hover */
  color: white;
}

.btn-save:disabled {
  background-color: rgba(
    0,
    0,
    0,
    0.06
  ) !important; /* Light gray with transparency */
  color: rgba(0, 0, 0, 0.3) !important; /* Dimmed text */
  border: 1px solid rgba(0, 0, 0, 0.1) !important; /* Light border */
  cursor: not-allowed;
  opacity: 0.7; /* Softened appearance */
}

.btn-cancel {
  background: #fff !important; /* Grayish color for cancel */
  height: 40px;
  font-family: "Poppins";
  font-weight: 400 !important;
  font-size: 14px !important;
  padding: 0 20px;
  margin-left: 20px;
  margin-bottom: 6px;
  border: 1px solid #d9d9d9;
  color: black;
}

.btn-cancel:hover {
  background: #f0f0f0 !important; /* Darker gray on hover */
  color: black;
}

.btn-cancel:focus {
  background: #6c757d !important;
  box-shadow: none !important;
}

/* edit pencil button */
.btn-edit-pencil svg {
  font-size: 20px;
  color: #ff8732;
}

/* search */

.search-box {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  width: 100%;
}

.search-input {
  flex: none;
  width: clamp(200px, 30%, 360px);
  height: 36px;
  padding: 0 16px;
  border: 2px solid #000 !important;
  border-radius: 18px;
  font-size: 14px;
  box-sizing: border-box;
}

.search-input::placeholder {
  color: #999;
}

.search-btn {
  flex: none;
  height: 38px;
  padding: 0 20px;
  border: none;
  border-radius: 18px;
  background-color: #ff8c00;
  color: white;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}
.clear-btn {
  flex: none;
  height: 38px;
  padding: 0 20px;
  border-radius: 18px;
  border: 1px solid #d9d9d9;
  background-color: #f5f5f5;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}
.clear-all-btn {
  flex: none;
  height: 38px;
  padding: 0 20px;
  border-radius: 18px;
  border: 1px solid #d9d9d9;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}

/* after antd.css is loaded */
.search-icone svg {
  background-color: #fff !important;
  border-radius: 50% !important;
  border: 1px solid #d9d9d9 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* login button */

.btn-login {
  background: #ff8732 !important;
  height: 40px;
  font-family: "Poppins";
  font-weight: 400 !important;
  font-size: 14px !important;
  padding: 0 20px;
  margin-bottom: 6px;
  border: none;
  color: white;
}

/* commom link styles */
.common-link {
  color: #ff9900;
  /* padding: 5px 10px; */
  text-decoration: none;
  display: inline-block;
}
.common-link:hover {
  text-decoration: underline;
  color: #ff9900;
}
