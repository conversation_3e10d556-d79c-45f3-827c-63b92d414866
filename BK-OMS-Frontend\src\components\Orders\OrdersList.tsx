import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { axiosInstance } from "../../apiCalls";
import { OrdersListProps } from "../../types";
import { <PERSON><PERSON>, Badge, Button, Input, InputRef, Pagination } from "antd";
import { useNavigate } from "react-router-dom";
import { Typography, DatePicker } from "antd";
import { ReloadOutlined, SearchOutlined } from "@ant-design/icons";
import dayjs, { Dayjs } from "dayjs";
import { useTableFilters } from "../../customHooks/useFilter";
import "../Products/Products.css";
import DataTable from "../UI/DataTable/DataTable";
import FilterButtons from "../UI/FilterButton";
import { FilterDropdownProps } from "antd/es/table/interface";
import StoreFilterDropdown from "./OrderFIlter/StoreNameFilter";
import FilterMenu from "../UI/FilterMenu";
import { handleApiError } from "../../utils/ApiErrorHandler";

const { Link } = Typography;
const { RangePicker } = DatePicker;

interface OrdersListUI extends OrdersListProps {
  key: number;
}

export interface Orders {
  objects: OrdersListProps[];
  page_size: number;
  current_page: number;
  total_pages: number;
  next_page: number | null;
  previous_page: number | null;
  total_count: number;
}

const dynamicAttributes = {
  payment_status: ["paid", "unpaid", "expired", "refunded", "refund_failed"],
  payment_method: ["cash", "upi_qr", "edc_qr", "card"],
  order_status: ["new", "completed", "processing"],
};

const OrdersList: React.FC = () => {
  const navigate = useNavigate();
  // const [form] = Form.useForm();
  const {
    currentPage,
    pageSize,
    filters,
    setFilters,
    appliedFilters,
    showClearButtons,
    updateURLParams,
    handlePageChange,
    handleFilterChange,
    clearFilter,
    clearAllFilters,
  } = useTableFilters();

  const [orders, setOrders] = useState<OrdersListProps[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [refreshToggle, setRefreshToggle] = useState<boolean>(false);
  // const [currentPage, setCurrentPage] = useState<number>(1);

  const codeInputRef = useRef<InputRef>(null);

  //const [searchValue, setSearchValue] = useState<string>("");

  const [error, setError] = useState<string | null>(null);

  const [totalCount, setTotalCount] = useState<number>(0);
  const [dateRange, setDateRange] = useState<
    [Dayjs | null, Dayjs | null] | null
  >(null);

  const memoizedFilters = useMemo(() => filters, [filters]);

  // console.log(filters);
  // console.log(handleFilterChange);

  // const getOrders = async (page: number) => {
  //   setLoading(true);
  //   try {
  //     const response = await axiosInstance.get(`api/pos/ncr-order/`, {
  //       params: { page },
  //     });
  //     if (response.status === 200) {
  //       setOrders(response.data);
  //       setLoading(false);
  //     } else {
  //       console.log("Error fetching menu data", response.status);
  //       setOrders(null);
  //       setLoading(false);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching menu data", error);
  //     setLoading(false);
  //   }
  // };

  useEffect(() => {
    const controller = new AbortController();
    const fetchOrders = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await axiosInstance.get<Orders>("api/pos/ncr-order/", {
          params: {
            page: currentPage,
            page_size: pageSize,
            ...memoizedFilters,
          },
          signal: controller.signal,
        });

        if (response.status === 200) {
          setOrders(response.data.objects);
          setTotalCount(response.data.total_count);
          setError(null);
        } else {
          setError("Unexpected response format.");
        }
      } catch (error: any) {
        if (error.name === "CanceledError" || error.name === "AbortError") {
          return;
        }
        console.error("Error fetching menu data", error);
        handleApiError(error, setError, navigate);
        setError(error.message || "Something went wrong");
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();

    return () => controller.abort();
  }, [currentPage, pageSize, memoizedFilters, refreshToggle]);

  const handleDateRangeChange = useCallback(
    (dates: [Dayjs | null, Dayjs | null] | null) => {
      if (dates) {
        const date_from = dates[0]?.format("YYYY-MM-DD");
        const date_to = dates[1]?.format("YYYY-MM-DD");
        const updatedFilters = { ...filters, date_from, date_to, page: "1" };
        setFilters(updatedFilters);
        updateURLParams(updatedFilters);
      } else {
        const resetFilters = { ...filters };
        delete resetFilters.date_from;
        delete resetFilters.date_to;
        setFilters(resetFilters);
        updateURLParams(resetFilters);
      }
    },
    [filters, setFilters, updateURLParams]
  );

  // const handleSearchChange = (value: string) => {
  //   handleFilterChange("search_value", value);
  // };

  const clearFilterHandler = (key: string) => {
    //console.log(key);
    clearFilter(key);
    // if (key === "search") {
    //   setSearchValue("");
    // } else if (key === "date_from" || key === "date_to") {
    //   setDateRange(null);
    // }
    if (key === "date_from" || key === "date_to") {
      setDateRange(null);
    }
  };

  const clearAllFiltersHandler = () => {
    clearAllFilters();
    // setSearchValue("");
    setDateRange(null);
  };

  const handleRefresh = useCallback(() => {
    setRefreshToggle((prev) => !prev);
  }, []);

  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";

    return text
      .replace(/[^a-zA-Z0-9\/\- ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  // useEffect(() => {
  //   getOrders(currentPage);
  // }, [currentPage]);

  const columns = [
    // {
    //   title: "Id",
    //   width: "15%",
    //   dataIndex: "id",
    //   key: "id",
    //   fixed: "left" as "left",

    // },
    {
      title: "Order ID",
      width: "10%",
      dataIndex: "order_id",
      key: "order_id",
      fixed: "left" as "left",
      filteredValue: filters.order_id ? ([filters.order_id] as string[]) : null,
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }: any) => (
        <div className="filter-dropdown">
          <Input
            ref={codeInputRef}
            placeholder="Search Order ID"
            value={selectedKeys[0]}
            onChange={(e) => {
              const value = e.target.value;
              setSelectedKeys(value ? [value] : []);
              if (!value) {
                clearFilters();
                confirm();
                handleFilterChange("order_id", "All");
              }
            }}
            onPressEnter={() => {
              confirm();
              handleFilterChange("order_id", selectedKeys[0]);
            }}
            autoFocus
            suffix={
              selectedKeys[0] ? (
                <span
                  className="search-filter-clear-btn"
                  onClick={() => {
                    setSelectedKeys([]);
                    clearFilters();
                    confirm();
                    handleFilterChange("order_id", "All");
                  }}
                >
                  ✖
                </span>
              ) : null
            }
          />
        </div>
      ),
      filterDropdownProps: {
        onOpenChange: (visible: boolean) => {
          if (visible) {
            setTimeout(() => codeInputRef.current?.focus(), 100);
          }
        },
      },
      filterIcon: (filtered: boolean) => (
        <div className={`filter-icon ${filtered ? "active" : "inactive"}`}>
          <SearchOutlined />
        </div>
      ),
      render: (text: string, record: OrdersListProps) => (
        <span onClick={() => navigate(`./${record.id}`)}>
          <Link>{text}</Link>
        </span>
      ),
    },
    {
      title: "Customer Name",
      width: "12%",
      dataIndex: "customer_name",
      key: "customer_name",
      //fixed: "left" as "left",
      filteredValue: filters.customer_name
        ? ([filters.customer_name] as string[])
        : null,
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }: any) => (
        <div className="filter-dropdown">
          <Input
            ref={codeInputRef}
            placeholder="Search Customer Name"
            value={selectedKeys[0]}
            onChange={(e) => {
              const value = e.target.value;
              setSelectedKeys(value ? [value] : []);
              if (!value) {
                clearFilters();
                confirm();
                handleFilterChange("customer_name", "All");
              }
            }}
            onPressEnter={() => {
              confirm();
              handleFilterChange("customer_name", selectedKeys[0]);
            }}
            autoFocus
            suffix={
              selectedKeys[0] ? (
                <span
                  className="search-filter-clear-btn"
                  onClick={() => {
                    setSelectedKeys([]);
                    clearFilters();
                    confirm();
                    handleFilterChange("customer_name", "All");
                  }}
                >
                  ✖
                </span>
              ) : null
            }
          />
        </div>
      ),
      filterDropdownProps: {
        onOpenChange: (visible: boolean) => {
          if (visible) {
            setTimeout(() => codeInputRef.current?.focus(), 100);
          }
        },
      },
      filterIcon: (filtered: boolean) => (
        <div className={`filter-icon ${filtered ? "active" : "inactive"}`}>
          <SearchOutlined />
        </div>
      ),
    },
    {
      title: "Store",
      dataIndex: "store",
      key: "store",
      width: "15%",
      filteredValue: filters.store ? ([filters.store] as string[]) : null,

      filterDropdown: useMemo(
        () =>
          ({
            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
          }: FilterDropdownProps) =>
            (
              <StoreFilterDropdown
                selectedKeys={selectedKeys as string[]}
                setSelectedKeys={setSelectedKeys}
                confirm={confirm}
                clearFilters={clearFilters ?? (() => {})}
                handleFilterChange={handleFilterChange}
                inputRef={codeInputRef}
                setFilters={setFilters}
                updateURLParams={updateURLParams}
                filters={filters}
              />
            ),
        [filters.store]
      ),
      filterDropdownProps: {
        onOpenChange: (visible: any) => {
          if (visible) {
            setTimeout(() => codeInputRef.current?.focus(), 100);
          }
        },
      },
      filterIcon: (filtered: boolean) => (
        <div className={`filter-icon ${filtered ? "active" : "inactive"}`}>
          <SearchOutlined />
        </div>
      ),
      // render: (text: string, record: OrdersListProps) =>
      //   text ? (
      //     <Link
      //       className="common-link text-decoration-none"
      //       to={`/stores/${record.store_id}/details`}
      //     >
      //       {text}
      //     </Link>
      //   ) : (
      //     "N/A"
      //   ),
    },
    // {
    //   title: "Channel",
    //   width: "15%",
    //   dataIndex: "channel",
    //   key: "channel",
    // },
    {
      title: "Is Order Processed",

      dataIndex: "is_order_processed",
      key: "is_order_processed",
      width: "10%",
      render: (is_order_processed: boolean) => (
        <Typography className="family-Poppins">
          {is_order_processed ? "Yes" : "No"}
        </Typography>
      ),
    },
    // {
    //   title: "Order Status",
    //   dataIndex: "order_status",
    //   key: "order_status",
    //   width: "12%",
    //   filteredValue: filters.order_status ? [filters.order_status] : null,
    //   filterDropdown: (props: any) => (
    //     <FilterMenu
    //       {...props}
    //       filterKey="order_status"
    //       options={dynamicAttributes?.order_status.map((status) => ({
    //         label: formatPaymentMethod(status),
    //         value: status,
    //       }))}
    //       handleFilterChange={handleFilterChange}
    //     />
    //   ),
    //   render: (status: string) => {
    //     const formattedClassName = String(status)
    //       .trim()
    //       .toLowerCase()
    //       .replace(/[\s_]+/g, "-");
    //     const statusClass = `status-wrapper ${formattedClassName}`;

    //     return (
    //       <div className={statusClass}>
    //         <Badge className="status-tag">{formatPaymentMethod(status)}</Badge>
    //       </div>
    //     );
    //   },
    // },
    {
      title: "Payment Status",
      dataIndex: "payment_status",
      key: "payment_status",
      width: "12%",
      // fixed: "right" as "right",
      filteredValue: filters.payment_status ? [filters.payment_status] : null,
      filterDropdown: (props: any) => (
        <FilterMenu
          {...props}
          filterKey="payment_status"
          options={dynamicAttributes?.payment_status.map((status) => ({
            label: formatPaymentMethod(status),
            value: status,
          }))}
          handleFilterChange={handleFilterChange}
        />
      ),
      render: (status: string) => {
        const formattedClassName = String(status)
          .trim()
          .toLowerCase()
          .replace(/[\s_]+/g, "-");
        const statusClass = `status-wrapper ${formattedClassName}`;

        return (
          <div className={statusClass}>
            <Badge className="status-tag">{formatPaymentMethod(status)}</Badge>
          </div>
        );
      },
    },
    {
      title: "Payment Method",
      dataIndex: "payment_method",
      key: "payment_method",
      width: "10%",
      filteredValue: filters.payment_method ? [filters.payment_method] : null,
      filterDropdown: (props: any) => (
        <FilterMenu
          {...props}
          filterKey="payment_method"
          options={dynamicAttributes.payment_method.map((status) => ({
            label: formatPaymentMethod(status),
            value: status,
          }))}
          handleFilterChange={handleFilterChange}
        />
      ),
      render: (value: string) => formatPaymentMethod(value),
    },
    {
      title: "Date",
      dataIndex: "created_at",
      key: "created_at",
      width: "20%",
      render: (createdAt: string) =>
        dayjs(createdAt).format("DD MMM YYYY, hh:mm: A"), // 12-hour format  //for 24-hour format: dayjs(createdAt).format("DD MMM 'YYYY, HH:mm:ss")
    },
  ];

  const data: OrdersListUI[] =
    orders?.map((order: OrdersListProps) => ({
      ...order,
      key: order.id,
      customer_name: order.customer_name || "-",

      payment_status: order.payment_status || "-",
      store: order.store || "-",
      channel: order.channel || "-",
      channel_id: order.channel_id || "-",
      order_id: order.order_id || "-",
    })) || [];

  if (!loading && error && orders.length === 0) {
    return (
      <div>
        <Alert message="Error" description={error} type="error" showIcon />
      </div>
    );
  }

  return (
    <div>
      <div className="container product-card-banner... ">
        <div className="header products-headers">
          <div className="title">Orders</div>
          <div className="d-flex align-items-center">
            <Button
              shape="round"
              onClick={handleRefresh}
              icon={loading ? <ReloadOutlined spin /> : <ReloadOutlined />}
            >
              Refresh
            </Button>
          </div>
          {/* <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            <input className="search-text" placeholder="Search" />
          </div> */}
        </div>
      </div>
      <div className="d-flex justify-content-end align-items-center flex-wrap">
        <div className="date-btn-driver">
          <RangePicker
            value={dateRange}
            onChange={(dates) => {
              setDateRange(dates);
              handleDateRangeChange(dates);
            }}
            format="YYYY-MM-DD"
          />
        </div>
        {/* <div className="search-btn-driver">
          <SearchBox
            value={searchValue}
            onChange={(v) => setSearchValue(v)}
            onSearch={() => handleFilterChange("search", searchValue)}
            placeholder="Enter Order id, store, customer name, etc."
          />
        </div> */}
      </div>
      <div className="mt-2">
        <div className="refresh-btn-driver d-flex justify-content-between align-items-center mb-3">
          {/* Clear Button Group - aligned to the start (left) */}
          <div className="d-flex align-items-center mt-2">
            <FilterButtons
              showClearButtons={showClearButtons}
              appliedFilters={appliedFilters}
              clearAllFilters={clearAllFiltersHandler}
              clearFilter={clearFilterHandler}
              formatFilterValue={formatPaymentMethod}
              filters={filters}
            />
          </div>
        </div>
      </div>
      <div>
        <DataTable<OrdersListProps>
          columns={columns}
          dataSource={data}
          pagination={false}
          loading={loading}
          rowKey="id"
          scroll={{ x: "max-content" }}
        />
        <div className="d-flex justify-content-end mt-3 mb-3">
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            showSizeChanger
            onShowSizeChange={handlePageChange}
            onChange={handlePageChange}
          />
        </div>
      </div>
    </div>
  );
};

export default OrdersList;
