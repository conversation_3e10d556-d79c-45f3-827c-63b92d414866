import { useEffect, useState } from "react";
import "../../Products/Products.css";
import { Table, message, Modal, Button, Typography } from "antd";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
// import { Typography } from "antd";
import { axiosInstance } from "../../../apiCalls";
const { Link } = Typography;

// const { Link } = Typography;

interface AttachedStoreCategory {
  id: number;
  store_name: string;
}

const AttachedStoreCategory: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [categories, setCategories] = useState<AttachedStoreCategory[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  // const [currentPage, setCurrentPage] = useState<number>(1);
  const [search, setSearch] = useState("");
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const [searchParams] = useSearchParams();
  const tab = searchParams.get("tab") || "AttachedStore";
  const categoryName = searchParams.get("name") || "Category Name";

  const getCategories = async (search: string) => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/v2/fetch-category-stores/${id}`,
        {
          params: { search },
        }
      );

      if (response.status === 201) {
        setCategories(response.data.data);
      } else {
        console.error("Error fetching categories", response.status);
        setCategories([]);
      }
    } catch (error) {
      console.error("Error fetching categories", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getCategories(search);
  }, []);

  const showDeleteConfirm = (categoryId: number) => {
    setDeleteId(categoryId);
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    if (!deleteId) return;
    try {
      await axiosInstance.delete(
        `api/menu/v2/delete-category-store/${deleteId}`
      );
      message.success("Category deleted successfully!");
      setCategories((prevCategories) =>
        prevCategories.filter((category) => category.id !== deleteId)
      );
    } catch (error) {
      message.error("Failed to delete category.");
      console.error("Error deleting category:", error);
    } finally {
      setIsDeleteModalOpen(false);
      setDeleteId(null);
    }
  };

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: "25%",
      fixed: "left" as "left",
      //   render: (text: string, record: AttachedStoreCategory) => (
      //     <span onClick={() => navigate(`./${record.id}`)}>
      //       <Link>{text}</Link>
      //     </span>
      //   ),
      // },
      render: (text: string, record: AttachedStoreCategory) => (
        <span onClick={() => navigate(`/stores/${record.id}`)}>
          <Link>{text}</Link>
        </span>
      ),
    },
    {
      title: "Store Name",
      dataIndex: "store_name",
      key: "store_name",
      width: "40%",
    },
    {
      title: "Actions",
      key: "actions",
      width: "15%",
      render: (_: any, record: AttachedStoreCategory) => (
        <Button
          type="primary"
          danger
          onClick={() => showDeleteConfirm(record.id)}
        >
          Delete
        </Button>
      ),
    },
  ];

  return (
    <div>
      <div className="container product-card-banner...........................">
        <div className="header products-headers">
          <div className="title">Attached Stores</div>
          <div className="search-container">
            <div className="search-box" />
            <div className="icon-container">
              <div className="icon-background"></div>
              <div className="icon-dot"></div>
              <div className="icon-overlay"></div>
            </div>
            <div className="search-container">
              <div className="button-serachs">
                <input
                  className="search-text"
                  placeholder="Search with name"
                  onChange={(e) => setSearch(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      getCategories(search);
                    }
                  }}
                />
                <button onClick={() => getCategories(search)}>Search</button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex justify-end">
        <button
          className="bg-orange-400 text-white px-4 py-2 rounded"
          onClick={() => {
            navigate(
              `/categories/${id}/setstores?tab=${tab}&name=${categoryName}`
            );
          }}
        >
          Set Stores
        </button>
      </div>

      <div className="pt-4 mt-4">
        <Table
          columns={columns}
          dataSource={categories.map((category) => ({
            ...category,
            key: category.id,
          }))}
          loading={loading}
          scroll={{ x: 800, y: 500 }}
        />
      </div>
      <Modal
        title="Confirm Deletion"
        open={isDeleteModalOpen}
        onOk={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
        okText="Delete"
        okType="danger"
        cancelText="Cancel"
      >
        <p>Are you sure you want to delete this store?</p>
      </Modal>
    </div>
  );
};

export default AttachedStoreCategory;
