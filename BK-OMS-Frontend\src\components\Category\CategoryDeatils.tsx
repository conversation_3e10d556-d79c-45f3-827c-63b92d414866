import React, {
  useEffect,
  useState,
  useRef,
  useMemo,
  useCallback,
} from "react";
import { useLocation } from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
import {  message, <PERSON><PERSON>, Modal } from "antd";
import { CSS } from "@dnd-kit/utilities";
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";

import { CategoryProductProps } from "../../types";
import "../Products/Products.css";
import BackButton from "../UI/BackButton";
import { CheckOutlined, CloseOutlined, EditOutlined } from "@ant-design/icons";
import axios from "axios";
import SearchBox from "../UI/SearchBox";
import { useTableFilters } from "../../customHooks/useFilter";
import DataTable from "../UI/DataTable/DataTable";

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  "data-row-key": string;
}
const DragRow: React.FC<RowProps & { dragEnabled: boolean }> = ({
  dragEnabled,
  ...props
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props["data-row-key"] });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: dragEnabled ? "move" : "default",
    ...(isDragging ? { position: "relative", zIndex: 9999 } : {}),
  };

  return (
    <tr
      {...props}
      ref={setNodeRef}
      style={style}
      {...(dragEnabled ? { ...attributes, ...listeners } : {})}
    />
  );
};
interface CategoryWithKey extends CategoryProductProps {
  key: string;
}

const CategoryDeatils: React.FC = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const storeId = queryParams.get("storeid");
  const categoryid = queryParams.get("id");
  const name = queryParams.get("name");
  const [editingCategoryId, _setEditingCategoryId] = useState<number | null>(
    null
  );

  const { handleFilterChange, filters } = useTableFilters();

  const [products, setProducts] = useState<CategoryWithKey[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [_editingPosition, setEditingPosition] = useState<number | null>(null);
  const positionInputRef = useRef<HTMLInputElement>(null);
  const codeInputRef = useRef<HTMLInputElement>(null);
  const [dragEnabled, setDragEnabled] = useState(false);

  const [search, setSearch] = useState<string>("");

  const memoizedFilters = useMemo(() => filters, [filters]);

  // const [loading, setLoading] = useState<boolean>(false);

  // const [_searchResults, setSearchResults] = useState<ModiferDataProps[]>([]);
  // const [editingId, setEditingId] = useState<number | null>(null);
  // const [editingPosition, setEditingPosition] = useState<number | null>(null);
  // const positionInputRef = useRef<any>(null);
  // const [_dat, setData] = useState<CategoryProductProps[]>([]);
  // // const [visible, setVisible] = useState(false);

  // const navigate = useNavigate();
  // const [form] = Form.useForm();

  // Function to fetch modifiers
  // const getSearchResult = async () => {
  //   // setLoading(true);
  //   try {
  //     const response = await axiosInstance.get(
  //       `api/menu/store-products/${storeId}/?search_query=${searchInput}`
  //     );
  //     if (response.status === 200) {
  //       setSearchResults(response.data.objects); // Set the results in the searchResults state
  //     }
  //   } catch (error) {
  //     console.error("Error fetching modifiers", error);
  //   } finally {
  //     // setLoading(false);
  //   }
  // };
  // const onSubmit = async (values: any) => {
  //   // Create payload object with product name and position

  //   try {
  //     const payload = {
  //       store_product: Number(storeProduct), // Convert storeProduct to a number
  //       position: Number(values.Postion), // Convert position to a number
  //       category: Number(categoryid),
  //       channel_service: Number(channelId),
  //     };
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (!dragEnabled || !over || active.id === over.id) return;

    const oldIndex = products.findIndex((c) => c.key === active.id);
    const newIndex = products.findIndex((c) => c.key === over.id);
    const newOrder = arrayMove(products, oldIndex, newIndex);
    setProducts(newOrder);
  };

  // const handleClearSearch = () => {
  //   setSearch("");
  //   handleFilterChange("search", "");
  // };
  const handleSaveNewOrder = async () => {
    try {
      const updatedmodifiers = products.map((item, idx) => ({
        ...item,
        position: idx + 1,
      }));
      const response = await axiosInstance.patch(
        `/api/menu/store-categories-variant-position-update/`,
        {
          list: updatedmodifiers,
        }
      );
      if (response.status === 200) {
        message.success("Products reordered successfully.");
        fetchProducts();
      }
    } catch {
      message.error("Failed to save new order.");
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 1 } })
  );

  // Fetch products in this category
  const fetchProducts = async () => {
    try {
      const res = await axiosInstance.get(
        `api/menu/store-categories/${categoryid}/`,
        {
          params: {
            ...memoizedFilters,
          },
        }
      );
      if (res.status === 200) {
        const sorted = res.data.products
          .map((item: any) => ({
            ...item,
            key: item.id.toString(),
          }))
          .sort((a: any, b: any) => a.position - b.position);

        setProducts(sorted);
      }
    } catch (err) {
      console.error(err);
      message.error("Failed to load products.");
    }
  };

  useEffect(() => {
    if (categoryid) fetchProducts();
  }, [categoryid, memoizedFilters]);

  // Unified updater for position & availability
  const updateCategoryVariant = async (
    categoryVariantId: number,
    newPosition: number,
    newAvailability: boolean
  ) => {
    setLoading(true);
    try {
      const res = await axiosInstance.put(
        `api/menu/store-categories-variant/${storeId}/${categoryVariantId}/`,
        {
          position: newPosition,
          is_available: newAvailability,
        }
      );
      if (res.status === 200) {
        message.success("Updated successfully.");
        fetchProducts();
      } else {
        message.error("Update failed.");
      }
    } catch (err: unknown) {
      if (axios.isAxiosError(err)) {
        message.error(
          err.response?.data?.message || "An error occurred while updating."
        );
      } else {
        message.error("An unexpected error occurred.");
      }
    } finally {
      setLoading(false);
      setEditingId(null);
      setEditingPosition(null);
    }
  };
  useEffect(() => {
    if (editingId !== null) {
      codeInputRef.current?.focus();
    }
  }, [editingId]);
  useEffect(() => {
    if (editingCategoryId !== null) {
      positionInputRef.current?.focus();
    }
  }, [editingCategoryId]);

  const handleStatusChange = (
    record: CategoryProductProps,
    newAvailable: boolean
  ) => {
    Modal.confirm({
      title: newAvailable ? "Activate Availability" : "Deactivate Availability",
      content: `Are you sure you want to ${
        newAvailable ? "activate" : "deactivate"
      } availability for “${record.display_name}”?`,
      okText: "Yes",
      cancelText: "No",
      className: "custom-modal",
      okButtonProps: { className: "custom-modal-ok-button" },
      cancelButtonProps: { className: "custom-modal-cancel-button" },
      onOk: () =>
        updateCategoryVariant(record.id, record.position, newAvailable),
    });
  };

  const handleClear = useCallback(() => {
    setSearch("");
    handleFilterChange("search", "");
  }, [handleFilterChange]);

  const columns = [
    { title: "Name", dataIndex: "name", key: "name", width: "20%" },
    {
      title: "Display Name",
      dataIndex: "display_name",
      key: "display_name",
      width: "10%",
    },
    {
      title: "Price",
      dataIndex: "price",
      key: "price",
      width: "10%",
      render: (price: number) => price.toFixed(2),
    },
    {
      title: "Discounted Price",
      dataIndex: "discounted_price",
      key: "discounted_price",
      width: "10%",
      render: (price: number) => price.toFixed(2),
    },
    {
      title: "Position",
      dataIndex: "position",
      key: "position",
      width: "10%",
      render: (_: any, record: CategoryProductProps) => (
        <>
          {record.position}
          <Button
            type="link"
            onClick={() => {
              setDragEnabled(true);
              message.info("Drag and drop is now enabled.");
            }}
          >
            <EditOutlined className="btn-edit-pencil" />
          </Button>
          {dragEnabled && (
            <Button
              icon={<CheckOutlined />}
              onClick={() => {
                setDragEnabled(false);
                handleSaveNewOrder();
              }}
            />
          )}
        </>
      ),
    },
    {
      title: "Visibility",
      dataIndex: "is_available",
      key: "is_available",
      width: "10%",
      render: (is_available: boolean, record: CategoryProductProps) => (
        <div
          className={`switch-button ${is_available ? "checked" : ""}`}
          onClick={() => handleStatusChange(record, !is_available)}
        >
          <span className="switch-label">
            {is_available ? <CheckOutlined /> : <CloseOutlined />}
          </span>
          <div className="switch-handle"></div>
        </div>
      ),
    },
  ];

  return (
    <div>
      <BackButton to={`/stores/${storeId}?tab=Store_Categories`} />
      <div className="main-dashboard-buttons" />
      <div className="container product-card-banner d-flex flex-wrap">
        <div className="header products-headers">
          <div className="title">Store Category &gt;&gt; {name}</div>

          <div className="d-flex flex-wrap">
            <SearchBox
              value={search}
              onChange={(v) => setSearch(v)}
              onSearch={() => handleFilterChange("search", search)}
              onClear={() => handleClear()}
              placeholder="Enter Name"
            />
          </div>
        </div>
      </div>
      <div className="pt-4 mt-4">
        <DndContext
          sensors={sensors}
          modifiers={[restrictToVerticalAxis]}
          onDragEnd={onDragEnd}
        >
          <SortableContext
            items={products.map((item) => item.key)}
            strategy={verticalListSortingStrategy}
          >
            <DataTable
              loading={loading}
              columns={columns}
              dataSource={products}
              components={{
                body: {
                  row: (props: RowProps) => (
                    <DragRow {...props} dragEnabled={dragEnabled} />
                  ),
                },
              }}
              rowKey="key"
            />
          </SortableContext>
        </DndContext>
      </div>
    </div>
  );
};

export default CategoryDeatils;
