import React, { useEffect, useState } from "react";
import { Card } from "antd";
import { useParams } from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
import { ModiferDataProps } from "../../types";

const tabList = [
  {
    key: "tab1",
    tab: "Details",
  },
  {
    key: "tab2",
    tab: "Groups",
  },
];

const ModifiersDetails: React.FC = () => {
  const { id } = useParams();
  const [activeTabKey1, setActiveTabKey1] = useState<string>("tab1");
  const [modifierDetails, setModifierDetails] =
    useState<ModiferDataProps | null>(null);

  const fetchModifer = async () => {
    try {
      const response = await axiosInstance.get(`/api/menu/modifiers/${id}`);
      if (response.status === 200) {
        setModifierDetails(response.data);
      } else {
        
        setModifierDetails(null);
      }
    } catch (error) {
      console.error("Error fetching menu data", error);
    }
  };
  console.log("modfierDetails:", modifierDetails);
  useEffect(() => {
    fetchModifer();
  }, []);
  const onTab1Change = (key: string) => {
    setActiveTabKey1(key);
  };

  const contentList: Record<string, React.ReactNode> = {
    tab1: modifierDetails ? (
      <div>
        <p>
          <strong>Code:</strong> {modifierDetails.code}
        </p>
        <p>
          <strong>Name:</strong> {modifierDetails.name}
        </p>
        <p>
          <strong>Display Name:</strong> {modifierDetails.display_name}
        </p>
        <p>
          <strong>Position:</strong> {modifierDetails.position}
        </p>
        <p>
          <strong>Price:</strong> {modifierDetails.price}
        </p>
        <p>
          <strong>Behavior:</strong> {modifierDetails.behavior}
        </p>
        <p>
          <strong>Pricing Scheme:</strong> {modifierDetails.pricing_scheme}
        </p>
        <p>
          <strong>Default Quantity:</strong> {modifierDetails.default_quantity}
        </p>
        <p>
          <strong>Product:</strong> {modifierDetails.product}
        </p>
        <p>
          <strong>Upsized Version:</strong> {modifierDetails.upsized_version || "N/A"}
        </p>
        <p>
          <strong>Is Active:</strong> {modifierDetails.is_active ? "Yes" : "No"}
        </p>
      </div>
    ) : (
      <p>Loading...</p>
    ),
    tab2: <p>Additional information about the modifier will go here.</p>,
  };
  

  return (
    <>

      <Card
        style={{ width: "100%" }}
        title="Modifier Details"
        tabList={tabList}
        activeTabKey={activeTabKey1}
        onTabChange={onTab1Change}
      >
        {contentList[activeTabKey1]}
      </Card>

      <br />
      <br />
    </>
  );
};

export default ModifiersDetails;
