import { useEffect, useState } from "react";
import { ProductVariantProps } from "../../../types/Products";
import { axiosInstance } from "../../../apiCalls";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { Card, Spin, notification, Button, Upload } from "antd";
import VariantModiferGroup from "./Modifier Groups/VariantModiferGroup";
import ProductVariantChildVariantsList from "./Child Varinants/ProductVariantChildVariantsList";
import type { RcFile } from "antd/es/upload";
import { EditOutlined, LoadingOutlined, PlusOutlined } from "@ant-design/icons";
import ListingProductVariantStores from "./Stores/ListingProductVariantStores";
import axios from "axios";
import BackButton from "../../UI/BackButton";

const tabList = [
  { key: "Details", tab: "Details" },
  { key: "ModifierGroups", tab: " Modifier Groups" },
  { key: "Child_Variants", tab: "Child Variants" },
  { key: "Stores", tab: "Stores" },
];

const ProductVariantDetails = () => {
  const [variantDetails, setVariantDetails] =
    useState<ProductVariantProps | null>(null);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const activeTabKey1 = searchParams.get("tab") || "Details";
  const { variantid } = useParams();
  const categoryId = searchParams.get("categoryId");
  const categoryName = searchParams.get("categoryName");
  const navigate = useNavigate();

  const fetchVariant = async () => {
    try {
      setLoading(true);
      const response = await axiosInstance.get(
        `api/menu/v2/product-variants/${variantid}/${variantid}/`
      );
      if (response.status === 200) {
        setVariantDetails(response.data);
      } else {
        setVariantDetails(null);
      }
    } catch (error) {
      console.error("Error fetching menu data", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVariant();
  }, []);

  const onTabChange = (key: string) => {
    setSearchParams({ tab: key });
  };

  const getPresignedUrl = async (file: RcFile) => {
    try {
      const response = await axiosInstance.post(
        "/api/utilities/get-file-upload-url/",
        {
          file_name: file.name,
          file_type: "thumbnail",
        }
      );

      const { url, fields } = response.data?.url || {};
      if (!url || !fields) return null;

      return { url, fields, key: fields.key };
    } catch (error) {
      notification.error({
        message: "Error",
        description: `Failed to get presigned URL for ${file.name}.`,
      });
      return null;
    }
  };

  const uploadToS3 = async (
    uploadData: any,
    file: RcFile,
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => {
    setUploading(true);
    try {
      const formData = new FormData();
      Object.entries(uploadData.fields).forEach(([key, value]) => {
        formData.append(key, value as string);
      });
      formData.append("file", file);

      await axios.post(uploadData.url, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });

      notification.success({
        message: "Upload Successful",
        description: `${file.name} uploaded successfully.`,
      });

      await updateProductImage(uploadData.key, fieldType);
    } catch (error) {
      notification.error({
        message: "Upload Failed",
        description: `Failed to upload ${file.name}.`,
      });
    } finally {
      setUploading(false);
    }
  };

  const handleUpload = async (
    file: RcFile,
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => {
    const uploadData = await getPresignedUrl(file);
    if (!uploadData) return;
    await uploadToS3(uploadData, file, fieldType);
  };

  const updateProductImage = async (
    imageUrl: string,
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => {
    try {
      await axiosInstance.patch(
        `api/menu/v2/product-variants/${variantid}/${variantid}/`,
        {
          [fieldType]: imageUrl,
        }
      );
      fetchVariant();
    } catch (error) {
      notification.error({
        message: "Update Failed",
        description: "Failed to update product image.",
      });
    }
  };

  const getUploadProps = (
    fieldType: "image_large_url" | "image_thumbnail_url"
  ) => ({
    beforeUpload: (file: RcFile) => {
      handleUpload(file, fieldType);
      return false;
    },
    showUploadList: false,
  });

  const uploadButton = (
    <div style={{ border: 0, background: "none", textAlign: "center" }}>
      {uploading ? (
        <LoadingOutlined style={{ fontSize: 24 }} />
      ) : (
        <PlusOutlined style={{ fontSize: 24 }} />
      )}
      <div style={{ marginTop: 8 }}>
        {uploading ? "Uploading..." : "Upload"}
      </div>
    </div>
  );

  const contentList: Record<string, React.ReactNode> = {
    Details: variantDetails ? (
      <div className="space-y-4">
        <p>
          <strong>Id:</strong> {variantDetails.id}
        </p>
        <p>
          <strong>Code:</strong> {variantDetails.code}
        </p>
        <p>
          <strong>Name:</strong> {variantDetails.name}
        </p>
        <p>
          <strong>SKU:</strong> {variantDetails.sku}
        </p>
        <p>
          <strong>Display Name:</strong> {variantDetails.display_name || "N/A"}
        </p>
        <p>
          <strong>Description:</strong> {variantDetails.description || "N/A"}
        </p>
        <p>
          <strong>Product Type:</strong> {variantDetails.product_type || "N/A"}
        </p>
        <p>
          <strong>Base Product:</strong> {variantDetails.base_product_name}
        </p>
        <p>
          <strong>Default Pop Variant:</strong>{" "}
          {variantDetails.default_pop_variant_name}
        </p>
        <p>
          <strong>Crown Product:</strong>{" "}
          {variantDetails.is_crown_product ? "Yes" : "No"}
        </p>
        {variantDetails.is_crown_product && (
          <div>
            <p>
              <strong>Loyalty Offer Code:</strong>{" "}
              {variantDetails.loyalty_offer_code}
            </p>
            <p>
              <strong>Crown Points:</strong> {variantDetails.crown_points}
            </p>
          </div>
        )}

        {/* Large Image */}
        <div>
          <strong>Large Image:</strong>
          {variantDetails.image_large_url ? (
            <div className="relative w-fit mt-2">
              <img
                src={String(variantDetails.image_large_url)}
                alt="Large"
                className="w-32 rounded-md shadow"
              />
              <Upload {...getUploadProps("image_large_url")}>
                <Button
                  icon={<EditOutlined />}
                  loading={uploading}
                  className="edit-upload-button"
                  // className="absolute top-1 right-1 p-1 bg-white rounded-full shadow hover:bg-gray-100"
                />
              </Upload>
            </div>
          ) : (
            <Upload
              listType="picture-card"
              {...getUploadProps("image_large_url")}
            >
              {uploadButton}
            </Upload>
          )}
        </div>

        {/* Thumbnail Image */}
        <div>
          <strong>Thumbnail:</strong>
          {variantDetails.image_thumbnail_url ? (
            <div className="relative w-fit mt-2">
              <img
                src={String(variantDetails.image_thumbnail_url)}
                alt="Thumbnail"
                className="w-20 rounded shadow"
              />
              <Upload {...getUploadProps("image_thumbnail_url")}>
                <Button
                  icon={<EditOutlined />}
                  loading={uploading}
                  className="edit-upload-button"
                  //className="absolute top-1 right-1 p-1 bg-white rounded-full shadow hover:bg-gray-100"
                />
              </Upload>
            </div>
          ) : (
            <Upload
              listType="picture-card"
              {...getUploadProps("image_thumbnail_url")}
            >
              {uploadButton}
            </Upload>
          )}
        </div>
      </div>
    ) : (
      <p>Loading...</p>
    ),
    ModifierGroups: <VariantModiferGroup />,
    Stores: <ListingProductVariantStores />,
    Child_Variants: <ProductVariantChildVariantsList />,
  };

  return (
    <>
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Spin size="large" />
        </div>
      ) : (
        <>
          <div>
            <BackButton
              to={
                categoryId && categoryName
                  ? `/categories/${categoryId}/?tab=ProductVariants&name=${categoryName}`
                  : `/variants`
              }
            />
          </div>
          <Card
            style={{ width: "100%" }}
            title={
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">
                  {variantDetails?.name}
                </h3>
                <div style={{ backgroundColor: "#ff8732" }}>
                  <Button
                    icon={<EditOutlined />}
                    type="text"
                    className="hover:text-blue-500"
                    onClick={() => {
                      // Add edit handler logic here
                      navigate(
                        `/variant/${variantDetails?.id}/EditProductVariant?name=${variantDetails?.name}`
                      );
                    }}
                  />
                </div>
              </div>
            }
            tabList={tabList}
            activeTabKey={activeTabKey1}
            onTabChange={onTabChange}
          >
            {contentList[activeTabKey1]}
          </Card>
        </>
      )}
    </>
  );
};

export default ProductVariantDetails;
