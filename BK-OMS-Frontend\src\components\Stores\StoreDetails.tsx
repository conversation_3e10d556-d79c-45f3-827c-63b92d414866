import React, { useEffect, useState } from "react";
import { Card, message, Spin } from "antd";
import { EditOutlined } from "@ant-design/icons";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { axiosInstance } from "../../apiCalls";
import { StoreDataProps } from "../../types";
import StoreModiferGroups from "./StroreModiferGroups/StoreModiferGroups";
import StoreCatagory from "./StoreCategory/StoreCatagory";
import "../Products/Products.css";
import StoreMenu from "./storeMenu/StoreMenu";
import ListStoreModifiers from "./StoreModifiers/ListStoreModifiers";
import BackButton from "../UI/BackButton";
// import ChildVariants from "../ChildVariants/ChildVariants";

const tabList = [
  { key: "Details", tab: "Details" },
  { key: "Store_Modifier_Groups", tab: "Store Modifier Groups" },
  { key: "Store_Categories", tab: "Store Categories" },
  { key: "Inventory", tab: "Inventory" },
  { key: "Store_Modifiers", tab: "Store Modifiers" },
  // { key: "Child_Variants", tab: "Child Variants" },
];

const StoreDetails: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [searchParams, setSearchParams] = useSearchParams();
  const activeTabKey1 = searchParams.get("tab") || "Details";

  const [storeDetails, setStoreDetails] = useState<StoreDataProps | null>(null);
  const [fileInput, setFileInput] = useState(false);
  const [loading, setLoading] = useState(false);
  const [file, setFile] = useState<File | null>(null);

  const fetchStore = async () => {
    try {
      const response = await axiosInstance.get(`/api/stores/${id}/`);
      if (response.status === 200) {
        setStoreDetails(response.data);
      } else {
        setStoreDetails(null);
      }
    } catch (error) {
      console.error("Error fetching menu data", error);
    }
  };
  // const downloadCSV = async () => {
  //   try {
  //     const response = await axiosInstance.get(
  //       `api/menu/download-store-inventory/${id}/`,
  //       { responseType: "blob" } // Ensure response is treated as a file
  //     );

  //     if (response.status === 200) {
  //       const blob = new Blob([response.data], { type: "text/csv" });

  //       const link = document.createElement("a");
  //       link.href = URL.createObjectURL(blob);
  //       link.download = "store-products.csv";

  //       document.body.appendChild(link);
  //       link.click();
  //       document.body.removeChild(link);
  //     }
  //   } catch (error) {
  //     console.error("Error downloading CSV:", error);
  //   }
  // };

  const uploadCSV = async (file: File) => {
    try {
      const formData = new FormData();
      formData.append("file", file);
      // setLoading(true);
      const response = await axiosInstance.put(
        `api/menu/update-store-inventory/${id}/`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      if (response.status === 200) {
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
      console.error("Error uploading CSV:", error);
    }
  };
  useEffect(() => {
    fetchStore();
  }, [id]);
  // useEffect(() => {
  //   const searchParams = new URLSearchParams(location.search);
  //   const tab = searchParams.get("tab");
  //   if (tab) {
  //     setActiveTabKey1(tab);
  //   }
  // }, [location.search]);
  // console.log(activeTabKey1);
  // Handle tab change and update the URL
  // const onTab1Change = (key: string) => {
  //   setActiveTabKey1(key);
  //   navigate(`/store/${id}?tab=${key}`);
  // };
  const onTabChange = (key: string) => {
    setSearchParams({ tab: key }); // Update the URL query param
  };
  // const syncProducts = async () => {
  //   try {
  //     const response = await axiosInstance.post(
  //       `api/menu/sync-store-products/${storeDetails?.third_party_id}/`
  //     );
  //     if (response.status === 200) {
  //       message.success(response.data.message);
  //     } else {
  //       message.error("Something went Wrong");
  //     }
  //   } catch (error) {
  //     message.error("Something went wrong");
  //   }
  // };
  const syncPromotions = async () => {
    try {
      const response = await axiosInstance.post(
        `api/menu/sync-store-promotions/${storeDetails?.third_party_id}/`
      );
      if (response.status === 200) {
        message.success(response.data.message);
      } else {
        message.error("Something went Wrong");
      }
    } catch (error) {
      message.error("Something went wrong");
    }
  };
  const contentList: Record<string, React.ReactNode> = {
    Details: storeDetails ? (
      <div className="flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Store Details</h2>
          <div>
            <button
              className="text-white px-4 py-2 rounded transition"
              style={{ backgroundColor: "#FF8732" }}
              onMouseOver={(e) =>
                (e.currentTarget.style.backgroundColor = "#e86f1a")
              }
              onMouseOut={(e) =>
                (e.currentTarget.style.backgroundColor = "#FF8732")
              }
              onClick={() => navigate(`/stores/${id}/edit`)}
            >
              <EditOutlined /> Edit
            </button>
          </div>
        </div>

        <div className="space-y-2">
          <p>
            <strong>Code:</strong> {storeDetails.code}
          </p>
          <p>
            <strong>Name:</strong> {storeDetails.name}
          </p>
          <p>
            <strong>POS Number:</strong> {storeDetails.ato_id || "-"}
          </p>
          <p>
            <strong>Time Zone:</strong> {storeDetails.timezone}
          </p>
          <p>
            <strong>Address:</strong> {storeDetails.address || "N/A"}
          </p>
          <p>
            <strong>Phone:</strong> {storeDetails.phone || "N/A"}
          </p>
          <p>
            <strong>Postal Code:</strong> {storeDetails.postal_code || "N/A"}
          </p>
          <p>
            <strong>Tax Percentage:</strong> {storeDetails.tax_percentage}%
          </p>
          <p>
            <strong>Delivery Order:</strong>{" "}
            {storeDetails.can_accept_delivery_order ? "Yes" : "No"}
          </p>
          <p>
            <strong>Is Active:</strong> {storeDetails.is_active ? "Yes" : "No"}
          </p>
          <p>
            <strong>Coverage Type:</strong> {storeDetails.coverage_type}
          </p>
          <p>
            <strong>Third Party ID:</strong>{" "}
            {storeDetails.third_party_id || "N/A"}
          </p>
          <p>
            <strong>Takeaway Charge:</strong> {storeDetails.take_away_charge}
          </p>
          <p>
            <strong>Cash Payment Available:</strong>{" "}
            {storeDetails.is_cash_payment_available ? "Yes" : "No"}
          </p>
          <p>
            <strong>Card Payment Available:</strong>{" "}
            {storeDetails.is_card_payment_available ? "Yes" : "No"}
          </p>
          <p>
            <strong>QR Code Payment Available:</strong>{" "}
            {storeDetails.is_qr_code_payment_available ? "Yes" : "No"}
          </p>
          <p>
            <strong>Business ID:</strong> {storeDetails.business}
          </p>
          <p>
            <strong>Store Config:</strong> {storeDetails.store_config || "N/A"}
          </p>
          <p>
            <strong>Banner Config:</strong>{" "}
            {storeDetails.banner_config || "N/A"}
          </p>
        </div>
      </div>
    ) : (
      <p>Loading...</p>
    ),
    Store_Modifier_Groups: <StoreModiferGroups storeId={id || ""} />,
    Store_Categories: <StoreCatagory id={id || ""} />,
    Inventory: <StoreMenu storeId={id || ""} />,
    Store_Modifiers: <ListStoreModifiers id={id || ""} />,
    // Child_Variants: <ChildVariants />,
  };

  return (
    <>
      {loading ? (
        <div className="loader-container">
          <Spin size="large" />
        </div>
      ) : (
        <>
          <div>
            <BackButton to={`/stores`} />
          </div>
          <Card
            style={{ width: "100%" }}
            title={<h3>{storeDetails?.name}</h3>}
            tabList={tabList}
            activeTabKey={activeTabKey1}
            onTabChange={onTabChange}
            extra={
              <div className="main-dashboard-buttons">
                {" "}
                <button className="typography  " onClick={syncPromotions}>
                  {" "}
                  + Sync Price{" "}
                </button>
                {/* <button className="typography" onClick={syncProducts}>
                {" "}
                + Sync Products{" "}
              </button> */}
                {/* <button className="typography" onClick={downloadCSV}>
                  {" "}
                  + Download Products CSV{" "}
                </button>{" "} */}
                {/* <button
                  className="typography"
                  onClick={() => {
                    setFileInput(!fileInput);
                  }}
                >
                  {" "}
                  + Bulk Update{" "}
                </button> */}
                {fileInput && (
                  <div>
                    <input
                      type="file"
                      accept=".csv"
                      onChange={(e) => {
                        if (e.target.files?.[0]) {
                          setFile(e.target.files[0]);
                          setFileInput(true);
                        }
                      }}
                    />
                    <button
                      onClick={() => {
                        if (!file) return;
                        setLoading(true);
                        uploadCSV(file);
                      }}
                    >
                      Submit
                    </button>
                  </div>
                )}
              </div>
            }
          >
            {contentList[activeTabKey1]}
          </Card>
        </>
      )}

      <br />
      <br />
    </>
  );
};

export default StoreDetails;
