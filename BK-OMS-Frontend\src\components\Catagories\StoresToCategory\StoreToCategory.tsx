import React, { useState, useEffect } from "react";
import { But<PERSON>, message, Table } from "antd";
import type { TableProps } from "antd";
import { StoreDataProps } from "../../../types/index";
import "../../Products/Products.css";
import {
  Link,
  useNavigate,
  useParams,
  useSearchParams,
} from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";

const StoreToCategory: React.FC = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [searchParams] = useSearchParams();
  const categoryName = searchParams.get("name") || "Category Name";
  const { id } = useParams();
  const [search, setSearch] = useState("");
  const [loading, setLoading] = useState(false);
  const [stores, setStores] = useState<StoreDataProps[]>([]); // ✅ now it's a flat array
  const [attachStores, setAttachStores] = useState<number[]>([]);
  const navigate = useNavigate();

  const getStores = async (searchQuery = "") => {
    setLoading(true);
    try {
      const response = await axiosInstance.get("api/stores/list/", {
        params: { search: searchQuery },
      });
      if (response.status === 200 && Array.isArray(response.data)) {
        setStores(response.data); // ✅ set the array directly
      } else {
        setStores([]);
      }
    } catch (error) {
      console.error("Error fetching store data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getStores();
  }, []);

  const onSelectChange = (
    newSelectedRowKeys: React.Key[],
    selectedRows: StoreDataProps[]
  ) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setAttachStores(selectedRows.map((store) => store.id));
  };

  const rowSelection: TableProps<StoreDataProps>["rowSelection"] = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const columns = [
    {
      title: "Id",
      dataIndex: "id",
      key: "id",
      width: 100,
      render: (text: string, record: StoreDataProps) => (
        <Link to={`./${record.id}`}>{text}</Link>
      ),
    },
    {
      title: "Code",
      dataIndex: "code",
      key: "code",
      width: 100,
      render: (text: string, record: StoreDataProps) => (
        <Link to={`./${record.id}`}>{text}</Link>
      ),
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: 200,
    },
    {
      title: "Status",
      dataIndex: "is_active",
      key: "is_active",
      width: 150,
      render: (isActive: boolean) => (isActive ? "Active" : "Inactive"),
    },
  ];

  const data = stores.map((store) => ({
    key: store.id,
    ...store,
  }));

  const attachStore = async () => {
    try {
      const response = await axiosInstance.post(
        `api/menu/v2/attach-store-category/${id}/`,
        {
          stores: attachStores,
        }
      );
      if (response.status === 201) {
        message.success(response.data.message);
        navigate(`/categories/${id}?tab=AttachedStore&name=${categoryName}`);
      }
    } catch (error: any) {
      message.error(error.response?.data?.message || "Failed to Attach Store");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="container category-card-banner">
        <div className="header categories-header">
          <div className="title">Stores</div>
          <div className="search-container">
              <div className="button-serachs">
                <input
                  className="search-text"
                  placeholder="Search with name"
                  onChange={(e) => setSearch(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      getStores(search);
                    }
                  }}
                />
                <button onClick={() => getStores(search)}>Search</button>
              </div>
            </div>
        </div>
      </div>
      <Button
        type="primary"
        onClick={attachStore}
        loading={loading}
        disabled={selectedRowKeys.length === 0}
      >
        Attach to Selected Stores
      </Button>

      <Table
        rowSelection={rowSelection}
        columns={columns}
        dataSource={data}
        loading={loading}
        scroll={{ x: "max-content" }}
      />
    </div>
  );
};

export default StoreToCategory;
