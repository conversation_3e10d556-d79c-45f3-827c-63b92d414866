// import React from 'react';
import { StoreProductVariantsProps } from "../../../../types/Products";
import { Alert, Typography } from "antd";
import { useParams } from "react-router-dom";

import DataTable from "../../../UI/DataTable/DataTable";
import { useEffect, useMemo, useState } from "react";
import { useTableFilters } from "../../../../customHooks/useFilter";
import SearchBox from "../../../UI/SearchBox";
import { axiosInstance } from "../../../../apiCalls";
//import CommonPagination from "../../../UI/Pagination/commonPagination";

const { Link } = Typography;

const ListingProductVariantStores = () => {
  // const navigate = useNavigate();
  const { variantid } = useParams() as { variantid: string };
  // const { code } = useParams() as { code: string };
  const [data, setData] = useState<StoreProductVariantsProps[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState("");

  const { handleFilterChange, clearFilter, currentPage, filters, pageSize } =
    useTableFilters();

  const memoizedFilters = useMemo(() => filters, [filters]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get(
        `api/menu/v2/store-product-variants/${variantid}/`,
        {
          params: {
            page: currentPage,
            page_size: pageSize,
            ...memoizedFilters,
          },
        }
      );
      if (response.status === 200) {
        setData(response.data);
      } else {
        setError("Failed to fetch data");
      }
    } catch (error) {
      setError("Failed to fetch data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [memoizedFilters]);

  // State for delete confirmation
  // const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  // const [deleteIds, setDeleteIds] = useState<{ parentId: number; childId: number } | null>(null);

  // Function to open the delete confirmation modal
  // const showDeleteConfirm = (parentId: number, childId: number) => {
  //   setDeleteIds({ parentId, childId });
  //   setIsDeleteModalOpen(true);
  // };

  // // Function to handle deletion
  // const handleDelete = async () => {
  //   if (!deleteIds) return;

  //   try {
  //     const response=await axiosInstance.delete(`api/menu/v2/child-variants/${deleteIds.parentId}/${deleteIds.childId}/`)
  //     if(response.status==204){
  //       message.success('successfully deleted child variants please refesh the page')
  //     }else{
  //       message.error("Failed to delete");
  //     }
  //   } catch (error) {
  //     message.error("Failed to delete");
  //   } finally {
  //     setIsDeleteModalOpen(false);
  //     setDeleteIds(null);
  //   }
  // };

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      fixed: "left" as "left",
      render: (text: string, _: StoreProductVariantsProps) => (
        <span onClick={() => console.log("Clicked ID")}>
          <Link>{text}</Link>
        </span>
      ),
    },
    {
      title: "Store ",
      dataIndex: "store_name",
      key: "store_name",
    },
    // {
    //   title: "Variant",
    //   dataIndex: "variant",
    //   key: "variant",
    // },
    // {
    //   title: "Category",
    //   dataIndex: "category",
    //   key: "category",
    // },
    // {
    //   title: "Position",
    //   dataIndex: "position",
    //   key: "position",
    // },
    {
      title: "Price",
      dataIndex: "price",
      key: "price",
    },
    // {
    //   title: "Delete",
    //   key: "delete",
    //   render: (_: any, record: StoreProductVariantsProps) => (
    //     <Button
    //       type="primary"
    //       danger
    //       onClick={() => showDeleteConfirm(record.parent, record.id)}
    //     >
    //       Delete
    //     </Button>
    //   ),
    // },
  ];

  const handleClear = () => {
    clearFilter("search");
    setSearch("");
  };

  // const mapData = (response: any): StoreProductVariantsProps[] => {
  //   if (!Array.isArray(response)) {
  //     console.error("Invalid API response format:", response);
  //     return [];
  //   }

  //   return response.map((item: any) => ({
  //     key: item.id,
  //     id: item.id,
  //     price: item.price,
  //     variant: item.variant,
  //     store_name: item.store_name,
  //     category:item.category,
  //     position: item.position,
  //   }));
  // };

  return (
    <>
      <div>
        {error ? (
          <>
            <div className="d-flex justify-content-center align-items-center">
              <Alert message={error} type="error" showIcon />
            </div>
          </>
        ) : (
          <>
            <div className="main-dashboard-buttons"></div>

            <div className="container product-card-banner">
              <div className="header products-headers">
                <div className="title">STORES</div>
                {/* <div className="search-container">
            <div className="button-serachs">
              <input
                className="search-text"
                placeholder="Search"
                onChange={(e) => setSearch(e.target.value)}
              />
              <button onClick={() => fetchData(search)}>Search</button>
            </div>
          </div> */}
                <div className="d-flex flex-wrap">
                  <SearchBox
                    value={search}
                    onChange={(v) => setSearch(v)}
                    onSearch={() => handleFilterChange("search", search)}
                    onClear={() => handleClear()}
                    placeholder="Enter Name"
                  />
                </div>
              </div>
            </div>

            <div className="pt-4 mt-4">
              <DataTable<StoreProductVariantsProps>
                rowKey={(record) => record.id}
                columns={columns}
                dataSource={data}
                loading={loading}
                pagination={{ pageSize: 10 }}
                
              />
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default ListingProductVariantStores;
